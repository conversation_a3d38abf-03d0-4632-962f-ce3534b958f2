ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-06-30T13:36:26',('Author'),(
    ''),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('UShape_Filleted_Both','UShape_Filleted_Both','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#1078);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#125,#230,#255,#279,#314,#412,#437,#462,#560,
    #595,#619,#644,#735,#765,#782,#799,#816,#833,#850,#867,#884,#901,
    #913,#930,#947,#964,#981,#998,#1015,#1032,#1049,#1066));
#17 = ADVANCED_FACE('',(#18),#120,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#39,#47,#56,#64,#72,#80,#89,#97,#106,#114));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,-3.552713678801E-15,18.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,0.,80.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,0.,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(0.,18.,-3.552713678801E-15));
#34 = CIRCLE('',#35,18.);
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(0.,18.,18.));
#37 = DIRECTION('',(1.,0.,-0.));
#38 = DIRECTION('',(0.,0.,1.));
#39 = ORIENTED_EDGE('',*,*,#40,.T.);
#40 = EDGE_CURVE('',#32,#41,#43,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(0.,102.,-3.552713678801E-15));
#43 = LINE('',#44,#45);
#44 = CARTESIAN_POINT('',(0.,0.,0.));
#45 = VECTOR('',#46,1.);
#46 = DIRECTION('',(-0.,1.,0.));
#47 = ORIENTED_EDGE('',*,*,#48,.T.);
#48 = EDGE_CURVE('',#41,#49,#51,.T.);
#49 = VERTEX_POINT('',#50);
#50 = CARTESIAN_POINT('',(0.,120.,18.));
#51 = CIRCLE('',#52,18.);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,102.,18.));
#54 = DIRECTION('',(1.,0.,-0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = ORIENTED_EDGE('',*,*,#57,.T.);
#57 = EDGE_CURVE('',#49,#58,#60,.T.);
#58 = VERTEX_POINT('',#59);
#59 = CARTESIAN_POINT('',(0.,120.,80.));
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(0.,120.,0.));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(0.,0.,1.));
#64 = ORIENTED_EDGE('',*,*,#65,.F.);
#65 = EDGE_CURVE('',#66,#58,#68,.T.);
#66 = VERTEX_POINT('',#67);
#67 = CARTESIAN_POINT('',(0.,112.,80.));
#68 = LINE('',#69,#70);
#69 = CARTESIAN_POINT('',(0.,0.,80.));
#70 = VECTOR('',#71,1.);
#71 = DIRECTION('',(-0.,1.,0.));
#72 = ORIENTED_EDGE('',*,*,#73,.F.);
#73 = EDGE_CURVE('',#74,#66,#76,.T.);
#74 = VERTEX_POINT('',#75);
#75 = CARTESIAN_POINT('',(0.,112.,18.));
#76 = LINE('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,112.,8.));
#78 = VECTOR('',#79,1.);
#79 = DIRECTION('',(0.,0.,1.));
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#74,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.,102.,8.));
#84 = CIRCLE('',#85,10.);
#85 = AXIS2_PLACEMENT_3D('',#86,#87,#88);
#86 = CARTESIAN_POINT('',(0.,102.,18.));
#87 = DIRECTION('',(1.,0.,-0.));
#88 = DIRECTION('',(0.,0.,1.));
#89 = ORIENTED_EDGE('',*,*,#90,.F.);
#90 = EDGE_CURVE('',#91,#82,#93,.T.);
#91 = VERTEX_POINT('',#92);
#92 = CARTESIAN_POINT('',(0.,18.,8.));
#93 = LINE('',#94,#95);
#94 = CARTESIAN_POINT('',(0.,8.,8.));
#95 = VECTOR('',#96,1.);
#96 = DIRECTION('',(-0.,1.,0.));
#97 = ORIENTED_EDGE('',*,*,#98,.F.);
#98 = EDGE_CURVE('',#99,#91,#101,.T.);
#99 = VERTEX_POINT('',#100);
#100 = CARTESIAN_POINT('',(0.,8.,18.));
#101 = CIRCLE('',#102,10.);
#102 = AXIS2_PLACEMENT_3D('',#103,#104,#105);
#103 = CARTESIAN_POINT('',(0.,18.,18.));
#104 = DIRECTION('',(1.,0.,-0.));
#105 = DIRECTION('',(0.,0.,1.));
#106 = ORIENTED_EDGE('',*,*,#107,.T.);
#107 = EDGE_CURVE('',#99,#108,#110,.T.);
#108 = VERTEX_POINT('',#109);
#109 = CARTESIAN_POINT('',(0.,8.,80.));
#110 = LINE('',#111,#112);
#111 = CARTESIAN_POINT('',(0.,8.,8.));
#112 = VECTOR('',#113,1.);
#113 = DIRECTION('',(0.,0.,1.));
#114 = ORIENTED_EDGE('',*,*,#115,.F.);
#115 = EDGE_CURVE('',#24,#108,#116,.T.);
#116 = LINE('',#117,#118);
#117 = CARTESIAN_POINT('',(0.,0.,80.));
#118 = VECTOR('',#119,1.);
#119 = DIRECTION('',(-0.,1.,0.));
#120 = PLANE('',#121);
#121 = AXIS2_PLACEMENT_3D('',#122,#123,#124);
#122 = CARTESIAN_POINT('',(0.,0.,0.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DIRECTION('',(0.,0.,1.));
#125 = ADVANCED_FACE('',(#126),#225,.F.);
#126 = FACE_BOUND('',#127,.F.);
#127 = EDGE_LOOP('',(#128,#138,#144,#145,#153,#161,#169,#177,#186,#194,
    #202,#210,#219));
#128 = ORIENTED_EDGE('',*,*,#129,.F.);
#129 = EDGE_CURVE('',#130,#132,#134,.T.);
#130 = VERTEX_POINT('',#131);
#131 = CARTESIAN_POINT('',(250.,-3.552713678801E-15,18.));
#132 = VERTEX_POINT('',#133);
#133 = CARTESIAN_POINT('',(250.,0.,30.));
#134 = LINE('',#135,#136);
#135 = CARTESIAN_POINT('',(250.,0.,0.));
#136 = VECTOR('',#137,1.);
#137 = DIRECTION('',(0.,0.,1.));
#138 = ORIENTED_EDGE('',*,*,#139,.F.);
#139 = EDGE_CURVE('',#22,#130,#140,.T.);
#140 = LINE('',#141,#142);
#141 = CARTESIAN_POINT('',(0.,-3.552713678801E-15,18.));
#142 = VECTOR('',#143,1.);
#143 = DIRECTION('',(1.,0.,-0.));
#144 = ORIENTED_EDGE('',*,*,#21,.T.);
#145 = ORIENTED_EDGE('',*,*,#146,.T.);
#146 = EDGE_CURVE('',#24,#147,#149,.T.);
#147 = VERTEX_POINT('',#148);
#148 = CARTESIAN_POINT('',(120.,0.,80.));
#149 = LINE('',#150,#151);
#150 = CARTESIAN_POINT('',(0.,0.,80.));
#151 = VECTOR('',#152,1.);
#152 = DIRECTION('',(1.,0.,-0.));
#153 = ORIENTED_EDGE('',*,*,#154,.F.);
#154 = EDGE_CURVE('',#155,#147,#157,.T.);
#155 = VERTEX_POINT('',#156);
#156 = CARTESIAN_POINT('',(120.,0.,35.));
#157 = LINE('',#158,#159);
#158 = CARTESIAN_POINT('',(120.,0.,35.));
#159 = VECTOR('',#160,1.);
#160 = DIRECTION('',(0.,0.,1.));
#161 = ORIENTED_EDGE('',*,*,#162,.F.);
#162 = EDGE_CURVE('',#163,#155,#165,.T.);
#163 = VERTEX_POINT('',#164);
#164 = CARTESIAN_POINT('',(120.,0.,30.));
#165 = LINE('',#166,#167);
#166 = CARTESIAN_POINT('',(120.,0.,30.));
#167 = VECTOR('',#168,1.);
#168 = DIRECTION('',(0.,0.,1.));
#169 = ORIENTED_EDGE('',*,*,#170,.T.);
#170 = EDGE_CURVE('',#163,#171,#173,.T.);
#171 = VERTEX_POINT('',#172);
#172 = CARTESIAN_POINT('',(168.,0.,30.));
#173 = LINE('',#174,#175);
#174 = CARTESIAN_POINT('',(120.,0.,30.));
#175 = VECTOR('',#176,1.);
#176 = DIRECTION('',(1.,0.,-0.));
#177 = ORIENTED_EDGE('',*,*,#178,.F.);
#178 = EDGE_CURVE('',#179,#171,#181,.T.);
#179 = VERTEX_POINT('',#180);
#180 = CARTESIAN_POINT('',(170.,0.,32.));
#181 = CIRCLE('',#182,2.);
#182 = AXIS2_PLACEMENT_3D('',#183,#184,#185);
#183 = CARTESIAN_POINT('',(170.,0.,30.));
#184 = DIRECTION('',(0.,1.,0.));
#185 = DIRECTION('',(0.,-0.,1.));
#186 = ORIENTED_EDGE('',*,*,#187,.T.);
#187 = EDGE_CURVE('',#179,#188,#190,.T.);
#188 = VERTEX_POINT('',#189);
#189 = CARTESIAN_POINT('',(170.,0.,35.));
#190 = LINE('',#191,#192);
#191 = CARTESIAN_POINT('',(170.,0.,30.));
#192 = VECTOR('',#193,1.);
#193 = DIRECTION('',(0.,0.,1.));
#194 = ORIENTED_EDGE('',*,*,#195,.T.);
#195 = EDGE_CURVE('',#188,#196,#198,.T.);
#196 = VERTEX_POINT('',#197);
#197 = CARTESIAN_POINT('',(200.,0.,35.));
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(120.,0.,35.));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(1.,0.,-0.));
#202 = ORIENTED_EDGE('',*,*,#203,.F.);
#203 = EDGE_CURVE('',#204,#196,#206,.T.);
#204 = VERTEX_POINT('',#205);
#205 = CARTESIAN_POINT('',(200.,0.,32.));
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(200.,0.,30.));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(0.,0.,1.));
#210 = ORIENTED_EDGE('',*,*,#211,.F.);
#211 = EDGE_CURVE('',#212,#204,#214,.T.);
#212 = VERTEX_POINT('',#213);
#213 = CARTESIAN_POINT('',(202.,0.,30.));
#214 = CIRCLE('',#215,2.);
#215 = AXIS2_PLACEMENT_3D('',#216,#217,#218);
#216 = CARTESIAN_POINT('',(200.,0.,30.));
#217 = DIRECTION('',(0.,1.,0.));
#218 = DIRECTION('',(0.,-0.,1.));
#219 = ORIENTED_EDGE('',*,*,#220,.T.);
#220 = EDGE_CURVE('',#212,#132,#221,.T.);
#221 = LINE('',#222,#223);
#222 = CARTESIAN_POINT('',(200.,0.,30.));
#223 = VECTOR('',#224,1.);
#224 = DIRECTION('',(1.,0.,-0.));
#225 = PLANE('',#226);
#226 = AXIS2_PLACEMENT_3D('',#227,#228,#229);
#227 = CARTESIAN_POINT('',(0.,0.,0.));
#228 = DIRECTION('',(-0.,1.,0.));
#229 = DIRECTION('',(0.,0.,1.));
#230 = ADVANCED_FACE('',(#231),#250,.T.);
#231 = FACE_BOUND('',#232,.T.);
#232 = EDGE_LOOP('',(#233,#234,#242,#249));
#233 = ORIENTED_EDGE('',*,*,#31,.T.);
#234 = ORIENTED_EDGE('',*,*,#235,.T.);
#235 = EDGE_CURVE('',#32,#236,#238,.T.);
#236 = VERTEX_POINT('',#237);
#237 = CARTESIAN_POINT('',(250.,18.,-3.552713678801E-15));
#238 = LINE('',#239,#240);
#239 = CARTESIAN_POINT('',(0.,18.,-3.552713678801E-15));
#240 = VECTOR('',#241,1.);
#241 = DIRECTION('',(1.,0.,-0.));
#242 = ORIENTED_EDGE('',*,*,#243,.F.);
#243 = EDGE_CURVE('',#130,#236,#244,.T.);
#244 = CIRCLE('',#245,18.);
#245 = AXIS2_PLACEMENT_3D('',#246,#247,#248);
#246 = CARTESIAN_POINT('',(250.,18.,18.));
#247 = DIRECTION('',(1.,0.,-0.));
#248 = DIRECTION('',(0.,0.,1.));
#249 = ORIENTED_EDGE('',*,*,#139,.F.);
#250 = CYLINDRICAL_SURFACE('',#251,18.);
#251 = AXIS2_PLACEMENT_3D('',#252,#253,#254);
#252 = CARTESIAN_POINT('',(0.,18.,18.));
#253 = DIRECTION('',(1.,0.,-0.));
#254 = DIRECTION('',(0.,-1.,0.));
#255 = ADVANCED_FACE('',(#256),#274,.T.);
#256 = FACE_BOUND('',#257,.T.);
#257 = EDGE_LOOP('',(#258,#259,#260,#268));
#258 = ORIENTED_EDGE('',*,*,#115,.F.);
#259 = ORIENTED_EDGE('',*,*,#146,.T.);
#260 = ORIENTED_EDGE('',*,*,#261,.T.);
#261 = EDGE_CURVE('',#147,#262,#264,.T.);
#262 = VERTEX_POINT('',#263);
#263 = CARTESIAN_POINT('',(120.,8.,80.));
#264 = LINE('',#265,#266);
#265 = CARTESIAN_POINT('',(120.,0.,80.));
#266 = VECTOR('',#267,1.);
#267 = DIRECTION('',(-0.,1.,0.));
#268 = ORIENTED_EDGE('',*,*,#269,.F.);
#269 = EDGE_CURVE('',#108,#262,#270,.T.);
#270 = LINE('',#271,#272);
#271 = CARTESIAN_POINT('',(0.,8.,80.));
#272 = VECTOR('',#273,1.);
#273 = DIRECTION('',(1.,0.,-0.));
#274 = PLANE('',#275);
#275 = AXIS2_PLACEMENT_3D('',#276,#277,#278);
#276 = CARTESIAN_POINT('',(0.,0.,80.));
#277 = DIRECTION('',(0.,0.,1.));
#278 = DIRECTION('',(1.,0.,-0.));
#279 = ADVANCED_FACE('',(#280,#298),#309,.F.);
#280 = FACE_BOUND('',#281,.F.);
#281 = EDGE_LOOP('',(#282,#283,#284,#292));
#282 = ORIENTED_EDGE('',*,*,#40,.F.);
#283 = ORIENTED_EDGE('',*,*,#235,.T.);
#284 = ORIENTED_EDGE('',*,*,#285,.T.);
#285 = EDGE_CURVE('',#236,#286,#288,.T.);
#286 = VERTEX_POINT('',#287);
#287 = CARTESIAN_POINT('',(250.,102.,-3.552713678801E-15));
#288 = LINE('',#289,#290);
#289 = CARTESIAN_POINT('',(250.,0.,0.));
#290 = VECTOR('',#291,1.);
#291 = DIRECTION('',(-0.,1.,0.));
#292 = ORIENTED_EDGE('',*,*,#293,.F.);
#293 = EDGE_CURVE('',#41,#286,#294,.T.);
#294 = LINE('',#295,#296);
#295 = CARTESIAN_POINT('',(0.,102.,-3.552713678801E-15));
#296 = VECTOR('',#297,1.);
#297 = DIRECTION('',(1.,0.,-0.));
#298 = FACE_BOUND('',#299,.F.);
#299 = EDGE_LOOP('',(#300));
#300 = ORIENTED_EDGE('',*,*,#301,.T.);
#301 = EDGE_CURVE('',#302,#302,#304,.T.);
#302 = VERTEX_POINT('',#303);
#303 = CARTESIAN_POINT('',(38.5,60.,0.));
#304 = CIRCLE('',#305,21.5);
#305 = AXIS2_PLACEMENT_3D('',#306,#307,#308);
#306 = CARTESIAN_POINT('',(60.,60.,0.));
#307 = DIRECTION('',(0.,0.,-1.));
#308 = DIRECTION('',(-1.,0.,-0.));
#309 = PLANE('',#310);
#310 = AXIS2_PLACEMENT_3D('',#311,#312,#313);
#311 = CARTESIAN_POINT('',(0.,0.,0.));
#312 = DIRECTION('',(0.,0.,1.));
#313 = DIRECTION('',(1.,0.,-0.));
#314 = ADVANCED_FACE('',(#315),#407,.T.);
#315 = FACE_BOUND('',#316,.T.);
#316 = EDGE_LOOP('',(#317,#327,#333,#334,#335,#343,#351,#359,#368,#376,
    #384,#392,#401));
#317 = ORIENTED_EDGE('',*,*,#318,.F.);
#318 = EDGE_CURVE('',#319,#321,#323,.T.);
#319 = VERTEX_POINT('',#320);
#320 = CARTESIAN_POINT('',(250.,8.,18.));
#321 = VERTEX_POINT('',#322);
#322 = CARTESIAN_POINT('',(250.,8.,30.));
#323 = LINE('',#324,#325);
#324 = CARTESIAN_POINT('',(250.,8.,8.));
#325 = VECTOR('',#326,1.);
#326 = DIRECTION('',(0.,0.,1.));
#327 = ORIENTED_EDGE('',*,*,#328,.F.);
#328 = EDGE_CURVE('',#99,#319,#329,.T.);
#329 = LINE('',#330,#331);
#330 = CARTESIAN_POINT('',(0.,8.,18.));
#331 = VECTOR('',#332,1.);
#332 = DIRECTION('',(1.,0.,-0.));
#333 = ORIENTED_EDGE('',*,*,#107,.T.);
#334 = ORIENTED_EDGE('',*,*,#269,.T.);
#335 = ORIENTED_EDGE('',*,*,#336,.T.);
#336 = EDGE_CURVE('',#262,#337,#339,.T.);
#337 = VERTEX_POINT('',#338);
#338 = CARTESIAN_POINT('',(120.,8.,35.));
#339 = LINE('',#340,#341);
#340 = CARTESIAN_POINT('',(120.,8.,21.5));
#341 = VECTOR('',#342,1.);
#342 = DIRECTION('',(-0.,0.,-1.));
#343 = ORIENTED_EDGE('',*,*,#344,.T.);
#344 = EDGE_CURVE('',#337,#345,#347,.T.);
#345 = VERTEX_POINT('',#346);
#346 = CARTESIAN_POINT('',(120.,8.,30.));
#347 = LINE('',#348,#349);
#348 = CARTESIAN_POINT('',(120.,8.,19.));
#349 = VECTOR('',#350,1.);
#350 = DIRECTION('',(-0.,0.,-1.));
#351 = ORIENTED_EDGE('',*,*,#352,.T.);
#352 = EDGE_CURVE('',#345,#353,#355,.T.);
#353 = VERTEX_POINT('',#354);
#354 = CARTESIAN_POINT('',(168.,8.,30.));
#355 = LINE('',#356,#357);
#356 = CARTESIAN_POINT('',(60.,8.,30.));
#357 = VECTOR('',#358,1.);
#358 = DIRECTION('',(1.,0.,-0.));
#359 = ORIENTED_EDGE('',*,*,#360,.F.);
#360 = EDGE_CURVE('',#361,#353,#363,.T.);
#361 = VERTEX_POINT('',#362);
#362 = CARTESIAN_POINT('',(170.,8.,32.));
#363 = CIRCLE('',#364,2.);
#364 = AXIS2_PLACEMENT_3D('',#365,#366,#367);
#365 = CARTESIAN_POINT('',(170.,8.,30.));
#366 = DIRECTION('',(0.,1.,0.));
#367 = DIRECTION('',(0.,-0.,1.));
#368 = ORIENTED_EDGE('',*,*,#369,.F.);
#369 = EDGE_CURVE('',#370,#361,#372,.T.);
#370 = VERTEX_POINT('',#371);
#371 = CARTESIAN_POINT('',(170.,8.,35.));
#372 = LINE('',#373,#374);
#373 = CARTESIAN_POINT('',(170.,8.,19.));
#374 = VECTOR('',#375,1.);
#375 = DIRECTION('',(-0.,0.,-1.));
#376 = ORIENTED_EDGE('',*,*,#377,.T.);
#377 = EDGE_CURVE('',#370,#378,#380,.T.);
#378 = VERTEX_POINT('',#379);
#379 = CARTESIAN_POINT('',(200.,8.,35.));
#380 = LINE('',#381,#382);
#381 = CARTESIAN_POINT('',(60.,8.,35.));
#382 = VECTOR('',#383,1.);
#383 = DIRECTION('',(1.,0.,-0.));
#384 = ORIENTED_EDGE('',*,*,#385,.T.);
#385 = EDGE_CURVE('',#378,#386,#388,.T.);
#386 = VERTEX_POINT('',#387);
#387 = CARTESIAN_POINT('',(200.,8.,32.));
#388 = LINE('',#389,#390);
#389 = CARTESIAN_POINT('',(200.,8.,19.));
#390 = VECTOR('',#391,1.);
#391 = DIRECTION('',(-0.,0.,-1.));
#392 = ORIENTED_EDGE('',*,*,#393,.F.);
#393 = EDGE_CURVE('',#394,#386,#396,.T.);
#394 = VERTEX_POINT('',#395);
#395 = CARTESIAN_POINT('',(202.,8.,30.));
#396 = CIRCLE('',#397,2.);
#397 = AXIS2_PLACEMENT_3D('',#398,#399,#400);
#398 = CARTESIAN_POINT('',(200.,8.,30.));
#399 = DIRECTION('',(0.,1.,0.));
#400 = DIRECTION('',(0.,-0.,1.));
#401 = ORIENTED_EDGE('',*,*,#402,.T.);
#402 = EDGE_CURVE('',#394,#321,#403,.T.);
#403 = LINE('',#404,#405);
#404 = CARTESIAN_POINT('',(100.,8.,30.));
#405 = VECTOR('',#406,1.);
#406 = DIRECTION('',(1.,0.,-0.));
#407 = PLANE('',#408);
#408 = AXIS2_PLACEMENT_3D('',#409,#410,#411);
#409 = CARTESIAN_POINT('',(0.,8.,8.));
#410 = DIRECTION('',(-0.,1.,0.));
#411 = DIRECTION('',(0.,0.,1.));
#412 = ADVANCED_FACE('',(#413),#432,.T.);
#413 = FACE_BOUND('',#414,.T.);
#414 = EDGE_LOOP('',(#415,#416,#424,#431));
#415 = ORIENTED_EDGE('',*,*,#48,.T.);
#416 = ORIENTED_EDGE('',*,*,#417,.T.);
#417 = EDGE_CURVE('',#49,#418,#420,.T.);
#418 = VERTEX_POINT('',#419);
#419 = CARTESIAN_POINT('',(250.,120.,18.));
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(0.,120.,18.));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(1.,0.,-0.));
#424 = ORIENTED_EDGE('',*,*,#425,.F.);
#425 = EDGE_CURVE('',#286,#418,#426,.T.);
#426 = CIRCLE('',#427,18.);
#427 = AXIS2_PLACEMENT_3D('',#428,#429,#430);
#428 = CARTESIAN_POINT('',(250.,102.,18.));
#429 = DIRECTION('',(1.,0.,-0.));
#430 = DIRECTION('',(0.,0.,1.));
#431 = ORIENTED_EDGE('',*,*,#293,.F.);
#432 = CYLINDRICAL_SURFACE('',#433,18.);
#433 = AXIS2_PLACEMENT_3D('',#434,#435,#436);
#434 = CARTESIAN_POINT('',(0.,102.,18.));
#435 = DIRECTION('',(1.,0.,-0.));
#436 = DIRECTION('',(0.,-0.,-1.));
#437 = ADVANCED_FACE('',(#438),#457,.F.);
#438 = FACE_BOUND('',#439,.F.);
#439 = EDGE_LOOP('',(#440,#441,#449,#456));
#440 = ORIENTED_EDGE('',*,*,#98,.T.);
#441 = ORIENTED_EDGE('',*,*,#442,.T.);
#442 = EDGE_CURVE('',#91,#443,#445,.T.);
#443 = VERTEX_POINT('',#444);
#444 = CARTESIAN_POINT('',(250.,18.,8.));
#445 = LINE('',#446,#447);
#446 = CARTESIAN_POINT('',(0.,18.,8.));
#447 = VECTOR('',#448,1.);
#448 = DIRECTION('',(1.,0.,-0.));
#449 = ORIENTED_EDGE('',*,*,#450,.F.);
#450 = EDGE_CURVE('',#319,#443,#451,.T.);
#451 = CIRCLE('',#452,10.);
#452 = AXIS2_PLACEMENT_3D('',#453,#454,#455);
#453 = CARTESIAN_POINT('',(250.,18.,18.));
#454 = DIRECTION('',(1.,0.,-0.));
#455 = DIRECTION('',(0.,0.,1.));
#456 = ORIENTED_EDGE('',*,*,#328,.F.);
#457 = CYLINDRICAL_SURFACE('',#458,10.);
#458 = AXIS2_PLACEMENT_3D('',#459,#460,#461);
#459 = CARTESIAN_POINT('',(0.,18.,18.));
#460 = DIRECTION('',(1.,0.,-0.));
#461 = DIRECTION('',(0.,-1.,0.));
#462 = ADVANCED_FACE('',(#463),#555,.T.);
#463 = FACE_BOUND('',#464,.T.);
#464 = EDGE_LOOP('',(#465,#473,#474,#475,#483,#491,#499,#507,#516,#524,
    #532,#540,#549));
#465 = ORIENTED_EDGE('',*,*,#466,.F.);
#466 = EDGE_CURVE('',#418,#467,#469,.T.);
#467 = VERTEX_POINT('',#468);
#468 = CARTESIAN_POINT('',(250.,120.,30.));
#469 = LINE('',#470,#471);
#470 = CARTESIAN_POINT('',(250.,120.,0.));
#471 = VECTOR('',#472,1.);
#472 = DIRECTION('',(0.,0.,1.));
#473 = ORIENTED_EDGE('',*,*,#417,.F.);
#474 = ORIENTED_EDGE('',*,*,#57,.T.);
#475 = ORIENTED_EDGE('',*,*,#476,.T.);
#476 = EDGE_CURVE('',#58,#477,#479,.T.);
#477 = VERTEX_POINT('',#478);
#478 = CARTESIAN_POINT('',(120.,120.,80.));
#479 = LINE('',#480,#481);
#480 = CARTESIAN_POINT('',(0.,120.,80.));
#481 = VECTOR('',#482,1.);
#482 = DIRECTION('',(1.,0.,-0.));
#483 = ORIENTED_EDGE('',*,*,#484,.F.);
#484 = EDGE_CURVE('',#485,#477,#487,.T.);
#485 = VERTEX_POINT('',#486);
#486 = CARTESIAN_POINT('',(120.,120.,35.));
#487 = LINE('',#488,#489);
#488 = CARTESIAN_POINT('',(120.,120.,35.));
#489 = VECTOR('',#490,1.);
#490 = DIRECTION('',(0.,0.,1.));
#491 = ORIENTED_EDGE('',*,*,#492,.F.);
#492 = EDGE_CURVE('',#493,#485,#495,.T.);
#493 = VERTEX_POINT('',#494);
#494 = CARTESIAN_POINT('',(120.,120.,30.));
#495 = LINE('',#496,#497);
#496 = CARTESIAN_POINT('',(120.,120.,30.));
#497 = VECTOR('',#498,1.);
#498 = DIRECTION('',(0.,0.,1.));
#499 = ORIENTED_EDGE('',*,*,#500,.T.);
#500 = EDGE_CURVE('',#493,#501,#503,.T.);
#501 = VERTEX_POINT('',#502);
#502 = CARTESIAN_POINT('',(168.,120.,30.));
#503 = LINE('',#504,#505);
#504 = CARTESIAN_POINT('',(120.,120.,30.));
#505 = VECTOR('',#506,1.);
#506 = DIRECTION('',(1.,0.,-0.));
#507 = ORIENTED_EDGE('',*,*,#508,.F.);
#508 = EDGE_CURVE('',#509,#501,#511,.T.);
#509 = VERTEX_POINT('',#510);
#510 = CARTESIAN_POINT('',(170.,120.,32.));
#511 = CIRCLE('',#512,2.);
#512 = AXIS2_PLACEMENT_3D('',#513,#514,#515);
#513 = CARTESIAN_POINT('',(170.,120.,30.));
#514 = DIRECTION('',(0.,1.,0.));
#515 = DIRECTION('',(0.,-0.,1.));
#516 = ORIENTED_EDGE('',*,*,#517,.T.);
#517 = EDGE_CURVE('',#509,#518,#520,.T.);
#518 = VERTEX_POINT('',#519);
#519 = CARTESIAN_POINT('',(170.,120.,35.));
#520 = LINE('',#521,#522);
#521 = CARTESIAN_POINT('',(170.,120.,30.));
#522 = VECTOR('',#523,1.);
#523 = DIRECTION('',(0.,0.,1.));
#524 = ORIENTED_EDGE('',*,*,#525,.T.);
#525 = EDGE_CURVE('',#518,#526,#528,.T.);
#526 = VERTEX_POINT('',#527);
#527 = CARTESIAN_POINT('',(200.,120.,35.));
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(120.,120.,35.));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(1.,0.,-0.));
#532 = ORIENTED_EDGE('',*,*,#533,.F.);
#533 = EDGE_CURVE('',#534,#526,#536,.T.);
#534 = VERTEX_POINT('',#535);
#535 = CARTESIAN_POINT('',(200.,120.,32.));
#536 = LINE('',#537,#538);
#537 = CARTESIAN_POINT('',(200.,120.,30.));
#538 = VECTOR('',#539,1.);
#539 = DIRECTION('',(0.,0.,1.));
#540 = ORIENTED_EDGE('',*,*,#541,.F.);
#541 = EDGE_CURVE('',#542,#534,#544,.T.);
#542 = VERTEX_POINT('',#543);
#543 = CARTESIAN_POINT('',(202.,120.,30.));
#544 = CIRCLE('',#545,2.);
#545 = AXIS2_PLACEMENT_3D('',#546,#547,#548);
#546 = CARTESIAN_POINT('',(200.,120.,30.));
#547 = DIRECTION('',(0.,1.,0.));
#548 = DIRECTION('',(0.,-0.,1.));
#549 = ORIENTED_EDGE('',*,*,#550,.T.);
#550 = EDGE_CURVE('',#542,#467,#551,.T.);
#551 = LINE('',#552,#553);
#552 = CARTESIAN_POINT('',(200.,120.,30.));
#553 = VECTOR('',#554,1.);
#554 = DIRECTION('',(1.,0.,-0.));
#555 = PLANE('',#556);
#556 = AXIS2_PLACEMENT_3D('',#557,#558,#559);
#557 = CARTESIAN_POINT('',(0.,120.,0.));
#558 = DIRECTION('',(-0.,1.,0.));
#559 = DIRECTION('',(0.,0.,1.));
#560 = ADVANCED_FACE('',(#561,#579),#590,.T.);
#561 = FACE_BOUND('',#562,.T.);
#562 = EDGE_LOOP('',(#563,#564,#565,#573));
#563 = ORIENTED_EDGE('',*,*,#90,.F.);
#564 = ORIENTED_EDGE('',*,*,#442,.T.);
#565 = ORIENTED_EDGE('',*,*,#566,.T.);
#566 = EDGE_CURVE('',#443,#567,#569,.T.);
#567 = VERTEX_POINT('',#568);
#568 = CARTESIAN_POINT('',(250.,102.,8.));
#569 = LINE('',#570,#571);
#570 = CARTESIAN_POINT('',(250.,8.,8.));
#571 = VECTOR('',#572,1.);
#572 = DIRECTION('',(-0.,1.,0.));
#573 = ORIENTED_EDGE('',*,*,#574,.F.);
#574 = EDGE_CURVE('',#82,#567,#575,.T.);
#575 = LINE('',#576,#577);
#576 = CARTESIAN_POINT('',(0.,102.,8.));
#577 = VECTOR('',#578,1.);
#578 = DIRECTION('',(1.,0.,-0.));
#579 = FACE_BOUND('',#580,.T.);
#580 = EDGE_LOOP('',(#581));
#581 = ORIENTED_EDGE('',*,*,#582,.T.);
#582 = EDGE_CURVE('',#583,#583,#585,.T.);
#583 = VERTEX_POINT('',#584);
#584 = CARTESIAN_POINT('',(38.5,60.,8.));
#585 = CIRCLE('',#586,21.5);
#586 = AXIS2_PLACEMENT_3D('',#587,#588,#589);
#587 = CARTESIAN_POINT('',(60.,60.,8.));
#588 = DIRECTION('',(0.,0.,-1.));
#589 = DIRECTION('',(-1.,0.,-0.));
#590 = PLANE('',#591);
#591 = AXIS2_PLACEMENT_3D('',#592,#593,#594);
#592 = CARTESIAN_POINT('',(0.,8.,8.));
#593 = DIRECTION('',(0.,0.,1.));
#594 = DIRECTION('',(1.,0.,-0.));
#595 = ADVANCED_FACE('',(#596),#614,.T.);
#596 = FACE_BOUND('',#597,.T.);
#597 = EDGE_LOOP('',(#598,#599,#607,#613));
#598 = ORIENTED_EDGE('',*,*,#65,.F.);
#599 = ORIENTED_EDGE('',*,*,#600,.T.);
#600 = EDGE_CURVE('',#66,#601,#603,.T.);
#601 = VERTEX_POINT('',#602);
#602 = CARTESIAN_POINT('',(120.,112.,80.));
#603 = LINE('',#604,#605);
#604 = CARTESIAN_POINT('',(0.,112.,80.));
#605 = VECTOR('',#606,1.);
#606 = DIRECTION('',(1.,0.,-0.));
#607 = ORIENTED_EDGE('',*,*,#608,.T.);
#608 = EDGE_CURVE('',#601,#477,#609,.T.);
#609 = LINE('',#610,#611);
#610 = CARTESIAN_POINT('',(120.,0.,80.));
#611 = VECTOR('',#612,1.);
#612 = DIRECTION('',(-0.,1.,0.));
#613 = ORIENTED_EDGE('',*,*,#476,.F.);
#614 = PLANE('',#615);
#615 = AXIS2_PLACEMENT_3D('',#616,#617,#618);
#616 = CARTESIAN_POINT('',(0.,0.,80.));
#617 = DIRECTION('',(0.,0.,1.));
#618 = DIRECTION('',(1.,0.,-0.));
#619 = ADVANCED_FACE('',(#620),#639,.F.);
#620 = FACE_BOUND('',#621,.F.);
#621 = EDGE_LOOP('',(#622,#623,#631,#638));
#622 = ORIENTED_EDGE('',*,*,#81,.T.);
#623 = ORIENTED_EDGE('',*,*,#624,.T.);
#624 = EDGE_CURVE('',#74,#625,#627,.T.);
#625 = VERTEX_POINT('',#626);
#626 = CARTESIAN_POINT('',(250.,112.,18.));
#627 = LINE('',#628,#629);
#628 = CARTESIAN_POINT('',(0.,112.,18.));
#629 = VECTOR('',#630,1.);
#630 = DIRECTION('',(1.,0.,-0.));
#631 = ORIENTED_EDGE('',*,*,#632,.F.);
#632 = EDGE_CURVE('',#567,#625,#633,.T.);
#633 = CIRCLE('',#634,10.);
#634 = AXIS2_PLACEMENT_3D('',#635,#636,#637);
#635 = CARTESIAN_POINT('',(250.,102.,18.));
#636 = DIRECTION('',(1.,0.,-0.));
#637 = DIRECTION('',(0.,0.,1.));
#638 = ORIENTED_EDGE('',*,*,#574,.F.);
#639 = CYLINDRICAL_SURFACE('',#640,10.);
#640 = AXIS2_PLACEMENT_3D('',#641,#642,#643);
#641 = CARTESIAN_POINT('',(0.,102.,18.));
#642 = DIRECTION('',(1.,0.,-0.));
#643 = DIRECTION('',(0.,-0.,-1.));
#644 = ADVANCED_FACE('',(#645),#730,.F.);
#645 = FACE_BOUND('',#646,.F.);
#646 = EDGE_LOOP('',(#647,#655,#656,#657,#658,#666,#674,#682,#691,#699,
    #707,#715,#724));
#647 = ORIENTED_EDGE('',*,*,#648,.F.);
#648 = EDGE_CURVE('',#625,#649,#651,.T.);
#649 = VERTEX_POINT('',#650);
#650 = CARTESIAN_POINT('',(250.,112.,30.));
#651 = LINE('',#652,#653);
#652 = CARTESIAN_POINT('',(250.,112.,8.));
#653 = VECTOR('',#654,1.);
#654 = DIRECTION('',(0.,0.,1.));
#655 = ORIENTED_EDGE('',*,*,#624,.F.);
#656 = ORIENTED_EDGE('',*,*,#73,.T.);
#657 = ORIENTED_EDGE('',*,*,#600,.T.);
#658 = ORIENTED_EDGE('',*,*,#659,.T.);
#659 = EDGE_CURVE('',#601,#660,#662,.T.);
#660 = VERTEX_POINT('',#661);
#661 = CARTESIAN_POINT('',(120.,112.,35.));
#662 = LINE('',#663,#664);
#663 = CARTESIAN_POINT('',(120.,112.,21.5));
#664 = VECTOR('',#665,1.);
#665 = DIRECTION('',(-0.,0.,-1.));
#666 = ORIENTED_EDGE('',*,*,#667,.T.);
#667 = EDGE_CURVE('',#660,#668,#670,.T.);
#668 = VERTEX_POINT('',#669);
#669 = CARTESIAN_POINT('',(120.,112.,30.));
#670 = LINE('',#671,#672);
#671 = CARTESIAN_POINT('',(120.,112.,19.));
#672 = VECTOR('',#673,1.);
#673 = DIRECTION('',(-0.,0.,-1.));
#674 = ORIENTED_EDGE('',*,*,#675,.T.);
#675 = EDGE_CURVE('',#668,#676,#678,.T.);
#676 = VERTEX_POINT('',#677);
#677 = CARTESIAN_POINT('',(168.,112.,30.));
#678 = LINE('',#679,#680);
#679 = CARTESIAN_POINT('',(60.,112.,30.));
#680 = VECTOR('',#681,1.);
#681 = DIRECTION('',(1.,0.,-0.));
#682 = ORIENTED_EDGE('',*,*,#683,.F.);
#683 = EDGE_CURVE('',#684,#676,#686,.T.);
#684 = VERTEX_POINT('',#685);
#685 = CARTESIAN_POINT('',(170.,112.,32.));
#686 = CIRCLE('',#687,2.);
#687 = AXIS2_PLACEMENT_3D('',#688,#689,#690);
#688 = CARTESIAN_POINT('',(170.,112.,30.));
#689 = DIRECTION('',(0.,1.,0.));
#690 = DIRECTION('',(0.,-0.,1.));
#691 = ORIENTED_EDGE('',*,*,#692,.F.);
#692 = EDGE_CURVE('',#693,#684,#695,.T.);
#693 = VERTEX_POINT('',#694);
#694 = CARTESIAN_POINT('',(170.,112.,35.));
#695 = LINE('',#696,#697);
#696 = CARTESIAN_POINT('',(170.,112.,19.));
#697 = VECTOR('',#698,1.);
#698 = DIRECTION('',(-0.,0.,-1.));
#699 = ORIENTED_EDGE('',*,*,#700,.T.);
#700 = EDGE_CURVE('',#693,#701,#703,.T.);
#701 = VERTEX_POINT('',#702);
#702 = CARTESIAN_POINT('',(200.,112.,35.));
#703 = LINE('',#704,#705);
#704 = CARTESIAN_POINT('',(60.,112.,35.));
#705 = VECTOR('',#706,1.);
#706 = DIRECTION('',(1.,0.,-0.));
#707 = ORIENTED_EDGE('',*,*,#708,.T.);
#708 = EDGE_CURVE('',#701,#709,#711,.T.);
#709 = VERTEX_POINT('',#710);
#710 = CARTESIAN_POINT('',(200.,112.,32.));
#711 = LINE('',#712,#713);
#712 = CARTESIAN_POINT('',(200.,112.,19.));
#713 = VECTOR('',#714,1.);
#714 = DIRECTION('',(-0.,0.,-1.));
#715 = ORIENTED_EDGE('',*,*,#716,.F.);
#716 = EDGE_CURVE('',#717,#709,#719,.T.);
#717 = VERTEX_POINT('',#718);
#718 = CARTESIAN_POINT('',(202.,112.,30.));
#719 = CIRCLE('',#720,2.);
#720 = AXIS2_PLACEMENT_3D('',#721,#722,#723);
#721 = CARTESIAN_POINT('',(200.,112.,30.));
#722 = DIRECTION('',(0.,1.,0.));
#723 = DIRECTION('',(0.,-0.,1.));
#724 = ORIENTED_EDGE('',*,*,#725,.T.);
#725 = EDGE_CURVE('',#717,#649,#726,.T.);
#726 = LINE('',#727,#728);
#727 = CARTESIAN_POINT('',(100.,112.,30.));
#728 = VECTOR('',#729,1.);
#729 = DIRECTION('',(1.,0.,-0.));
#730 = PLANE('',#731);
#731 = AXIS2_PLACEMENT_3D('',#732,#733,#734);
#732 = CARTESIAN_POINT('',(0.,112.,8.));
#733 = DIRECTION('',(-0.,1.,0.));
#734 = DIRECTION('',(0.,0.,1.));
#735 = ADVANCED_FACE('',(#736),#760,.T.);
#736 = FACE_BOUND('',#737,.T.);
#737 = EDGE_LOOP('',(#738,#739,#740,#741,#742,#743,#749,#750,#751,#752,
    #753,#754));
#738 = ORIENTED_EDGE('',*,*,#129,.F.);
#739 = ORIENTED_EDGE('',*,*,#243,.T.);
#740 = ORIENTED_EDGE('',*,*,#285,.T.);
#741 = ORIENTED_EDGE('',*,*,#425,.T.);
#742 = ORIENTED_EDGE('',*,*,#466,.T.);
#743 = ORIENTED_EDGE('',*,*,#744,.F.);
#744 = EDGE_CURVE('',#649,#467,#745,.T.);
#745 = LINE('',#746,#747);
#746 = CARTESIAN_POINT('',(250.,0.,30.));
#747 = VECTOR('',#748,1.);
#748 = DIRECTION('',(-0.,1.,0.));
#749 = ORIENTED_EDGE('',*,*,#648,.F.);
#750 = ORIENTED_EDGE('',*,*,#632,.F.);
#751 = ORIENTED_EDGE('',*,*,#566,.F.);
#752 = ORIENTED_EDGE('',*,*,#450,.F.);
#753 = ORIENTED_EDGE('',*,*,#318,.T.);
#754 = ORIENTED_EDGE('',*,*,#755,.F.);
#755 = EDGE_CURVE('',#132,#321,#756,.T.);
#756 = LINE('',#757,#758);
#757 = CARTESIAN_POINT('',(250.,0.,30.));
#758 = VECTOR('',#759,1.);
#759 = DIRECTION('',(-0.,1.,0.));
#760 = PLANE('',#761);
#761 = AXIS2_PLACEMENT_3D('',#762,#763,#764);
#762 = CARTESIAN_POINT('',(250.,0.,0.));
#763 = DIRECTION('',(1.,0.,-0.));
#764 = DIRECTION('',(0.,0.,1.));
#765 = ADVANCED_FACE('',(#766),#777,.T.);
#766 = FACE_BOUND('',#767,.T.);
#767 = EDGE_LOOP('',(#768,#774,#775,#776));
#768 = ORIENTED_EDGE('',*,*,#769,.F.);
#769 = EDGE_CURVE('',#212,#394,#770,.T.);
#770 = LINE('',#771,#772);
#771 = CARTESIAN_POINT('',(202.,0.,30.));
#772 = VECTOR('',#773,1.);
#773 = DIRECTION('',(0.,1.,0.));
#774 = ORIENTED_EDGE('',*,*,#220,.T.);
#775 = ORIENTED_EDGE('',*,*,#755,.T.);
#776 = ORIENTED_EDGE('',*,*,#402,.F.);
#777 = PLANE('',#778);
#778 = AXIS2_PLACEMENT_3D('',#779,#780,#781);
#779 = CARTESIAN_POINT('',(200.,0.,30.));
#780 = DIRECTION('',(0.,0.,1.));
#781 = DIRECTION('',(1.,0.,-0.));
#782 = ADVANCED_FACE('',(#783),#794,.F.);
#783 = FACE_BOUND('',#784,.F.);
#784 = EDGE_LOOP('',(#785,#791,#792,#793));
#785 = ORIENTED_EDGE('',*,*,#786,.T.);
#786 = EDGE_CURVE('',#204,#386,#787,.T.);
#787 = LINE('',#788,#789);
#788 = CARTESIAN_POINT('',(200.,0.,32.));
#789 = VECTOR('',#790,1.);
#790 = DIRECTION('',(0.,1.,0.));
#791 = ORIENTED_EDGE('',*,*,#393,.F.);
#792 = ORIENTED_EDGE('',*,*,#769,.F.);
#793 = ORIENTED_EDGE('',*,*,#211,.T.);
#794 = CYLINDRICAL_SURFACE('',#795,2.);
#795 = AXIS2_PLACEMENT_3D('',#796,#797,#798);
#796 = CARTESIAN_POINT('',(200.,0.,30.));
#797 = DIRECTION('',(0.,1.,0.));
#798 = DIRECTION('',(0.,-0.,1.));
#799 = ADVANCED_FACE('',(#800),#811,.T.);
#800 = FACE_BOUND('',#801,.T.);
#801 = EDGE_LOOP('',(#802,#803,#804,#805));
#802 = ORIENTED_EDGE('',*,*,#203,.F.);
#803 = ORIENTED_EDGE('',*,*,#786,.T.);
#804 = ORIENTED_EDGE('',*,*,#385,.F.);
#805 = ORIENTED_EDGE('',*,*,#806,.F.);
#806 = EDGE_CURVE('',#196,#378,#807,.T.);
#807 = LINE('',#808,#809);
#808 = CARTESIAN_POINT('',(200.,0.,35.));
#809 = VECTOR('',#810,1.);
#810 = DIRECTION('',(-0.,1.,0.));
#811 = PLANE('',#812);
#812 = AXIS2_PLACEMENT_3D('',#813,#814,#815);
#813 = CARTESIAN_POINT('',(200.,0.,30.));
#814 = DIRECTION('',(1.,0.,-0.));
#815 = DIRECTION('',(0.,0.,1.));
#816 = ADVANCED_FACE('',(#817),#828,.T.);
#817 = FACE_BOUND('',#818,.T.);
#818 = EDGE_LOOP('',(#819,#825,#826,#827));
#819 = ORIENTED_EDGE('',*,*,#820,.F.);
#820 = EDGE_CURVE('',#188,#370,#821,.T.);
#821 = LINE('',#822,#823);
#822 = CARTESIAN_POINT('',(170.,0.,35.));
#823 = VECTOR('',#824,1.);
#824 = DIRECTION('',(-0.,1.,0.));
#825 = ORIENTED_EDGE('',*,*,#195,.T.);
#826 = ORIENTED_EDGE('',*,*,#806,.T.);
#827 = ORIENTED_EDGE('',*,*,#377,.F.);
#828 = PLANE('',#829);
#829 = AXIS2_PLACEMENT_3D('',#830,#831,#832);
#830 = CARTESIAN_POINT('',(120.,0.,35.));
#831 = DIRECTION('',(0.,0.,1.));
#832 = DIRECTION('',(1.,0.,-0.));
#833 = ADVANCED_FACE('',(#834),#845,.F.);
#834 = FACE_BOUND('',#835,.F.);
#835 = EDGE_LOOP('',(#836,#837,#843,#844));
#836 = ORIENTED_EDGE('',*,*,#187,.F.);
#837 = ORIENTED_EDGE('',*,*,#838,.T.);
#838 = EDGE_CURVE('',#179,#361,#839,.T.);
#839 = LINE('',#840,#841);
#840 = CARTESIAN_POINT('',(170.,0.,32.));
#841 = VECTOR('',#842,1.);
#842 = DIRECTION('',(0.,1.,0.));
#843 = ORIENTED_EDGE('',*,*,#369,.F.);
#844 = ORIENTED_EDGE('',*,*,#820,.F.);
#845 = PLANE('',#846);
#846 = AXIS2_PLACEMENT_3D('',#847,#848,#849);
#847 = CARTESIAN_POINT('',(170.,0.,30.));
#848 = DIRECTION('',(1.,0.,-0.));
#849 = DIRECTION('',(0.,0.,1.));
#850 = ADVANCED_FACE('',(#851),#862,.F.);
#851 = FACE_BOUND('',#852,.F.);
#852 = EDGE_LOOP('',(#853,#854,#855,#856));
#853 = ORIENTED_EDGE('',*,*,#360,.F.);
#854 = ORIENTED_EDGE('',*,*,#838,.F.);
#855 = ORIENTED_EDGE('',*,*,#178,.T.);
#856 = ORIENTED_EDGE('',*,*,#857,.T.);
#857 = EDGE_CURVE('',#171,#353,#858,.T.);
#858 = LINE('',#859,#860);
#859 = CARTESIAN_POINT('',(168.,0.,30.));
#860 = VECTOR('',#861,1.);
#861 = DIRECTION('',(0.,1.,0.));
#862 = CYLINDRICAL_SURFACE('',#863,2.);
#863 = AXIS2_PLACEMENT_3D('',#864,#865,#866);
#864 = CARTESIAN_POINT('',(170.,0.,30.));
#865 = DIRECTION('',(0.,1.,0.));
#866 = DIRECTION('',(0.,-0.,1.));
#867 = ADVANCED_FACE('',(#868),#879,.T.);
#868 = FACE_BOUND('',#869,.T.);
#869 = EDGE_LOOP('',(#870,#876,#877,#878));
#870 = ORIENTED_EDGE('',*,*,#871,.F.);
#871 = EDGE_CURVE('',#163,#345,#872,.T.);
#872 = LINE('',#873,#874);
#873 = CARTESIAN_POINT('',(120.,0.,30.));
#874 = VECTOR('',#875,1.);
#875 = DIRECTION('',(-0.,1.,0.));
#876 = ORIENTED_EDGE('',*,*,#170,.T.);
#877 = ORIENTED_EDGE('',*,*,#857,.T.);
#878 = ORIENTED_EDGE('',*,*,#352,.F.);
#879 = PLANE('',#880);
#880 = AXIS2_PLACEMENT_3D('',#881,#882,#883);
#881 = CARTESIAN_POINT('',(120.,0.,30.));
#882 = DIRECTION('',(0.,0.,1.));
#883 = DIRECTION('',(1.,0.,-0.));
#884 = ADVANCED_FACE('',(#885),#896,.T.);
#885 = FACE_BOUND('',#886,.T.);
#886 = EDGE_LOOP('',(#887,#888,#889,#890));
#887 = ORIENTED_EDGE('',*,*,#162,.F.);
#888 = ORIENTED_EDGE('',*,*,#871,.T.);
#889 = ORIENTED_EDGE('',*,*,#344,.F.);
#890 = ORIENTED_EDGE('',*,*,#891,.F.);
#891 = EDGE_CURVE('',#155,#337,#892,.T.);
#892 = LINE('',#893,#894);
#893 = CARTESIAN_POINT('',(120.,0.,35.));
#894 = VECTOR('',#895,1.);
#895 = DIRECTION('',(-0.,1.,0.));
#896 = PLANE('',#897);
#897 = AXIS2_PLACEMENT_3D('',#898,#899,#900);
#898 = CARTESIAN_POINT('',(120.,0.,30.));
#899 = DIRECTION('',(1.,0.,-0.));
#900 = DIRECTION('',(0.,0.,1.));
#901 = ADVANCED_FACE('',(#902),#908,.T.);
#902 = FACE_BOUND('',#903,.T.);
#903 = EDGE_LOOP('',(#904,#905,#906,#907));
#904 = ORIENTED_EDGE('',*,*,#154,.F.);
#905 = ORIENTED_EDGE('',*,*,#891,.T.);
#906 = ORIENTED_EDGE('',*,*,#336,.F.);
#907 = ORIENTED_EDGE('',*,*,#261,.F.);
#908 = PLANE('',#909);
#909 = AXIS2_PLACEMENT_3D('',#910,#911,#912);
#910 = CARTESIAN_POINT('',(120.,0.,35.));
#911 = DIRECTION('',(1.,0.,-0.));
#912 = DIRECTION('',(0.,0.,1.));
#913 = ADVANCED_FACE('',(#914),#925,.F.);
#914 = FACE_BOUND('',#915,.F.);
#915 = EDGE_LOOP('',(#916,#922,#923,#924));
#916 = ORIENTED_EDGE('',*,*,#917,.T.);
#917 = EDGE_CURVE('',#583,#302,#918,.T.);
#918 = LINE('',#919,#920);
#919 = CARTESIAN_POINT('',(38.5,60.,80.));
#920 = VECTOR('',#921,1.);
#921 = DIRECTION('',(0.,0.,-1.));
#922 = ORIENTED_EDGE('',*,*,#301,.F.);
#923 = ORIENTED_EDGE('',*,*,#917,.F.);
#924 = ORIENTED_EDGE('',*,*,#582,.T.);
#925 = CYLINDRICAL_SURFACE('',#926,21.5);
#926 = AXIS2_PLACEMENT_3D('',#927,#928,#929);
#927 = CARTESIAN_POINT('',(60.,60.,80.));
#928 = DIRECTION('',(0.,0.,-1.));
#929 = DIRECTION('',(-1.,0.,-0.));
#930 = ADVANCED_FACE('',(#931),#942,.T.);
#931 = FACE_BOUND('',#932,.T.);
#932 = EDGE_LOOP('',(#933,#939,#940,#941));
#933 = ORIENTED_EDGE('',*,*,#934,.F.);
#934 = EDGE_CURVE('',#717,#542,#935,.T.);
#935 = LINE('',#936,#937);
#936 = CARTESIAN_POINT('',(202.,0.,30.));
#937 = VECTOR('',#938,1.);
#938 = DIRECTION('',(0.,1.,0.));
#939 = ORIENTED_EDGE('',*,*,#725,.T.);
#940 = ORIENTED_EDGE('',*,*,#744,.T.);
#941 = ORIENTED_EDGE('',*,*,#550,.F.);
#942 = PLANE('',#943);
#943 = AXIS2_PLACEMENT_3D('',#944,#945,#946);
#944 = CARTESIAN_POINT('',(200.,0.,30.));
#945 = DIRECTION('',(0.,0.,1.));
#946 = DIRECTION('',(1.,0.,-0.));
#947 = ADVANCED_FACE('',(#948),#959,.F.);
#948 = FACE_BOUND('',#949,.F.);
#949 = EDGE_LOOP('',(#950,#956,#957,#958));
#950 = ORIENTED_EDGE('',*,*,#951,.T.);
#951 = EDGE_CURVE('',#709,#534,#952,.T.);
#952 = LINE('',#953,#954);
#953 = CARTESIAN_POINT('',(200.,0.,32.));
#954 = VECTOR('',#955,1.);
#955 = DIRECTION('',(0.,1.,0.));
#956 = ORIENTED_EDGE('',*,*,#541,.F.);
#957 = ORIENTED_EDGE('',*,*,#934,.F.);
#958 = ORIENTED_EDGE('',*,*,#716,.T.);
#959 = CYLINDRICAL_SURFACE('',#960,2.);
#960 = AXIS2_PLACEMENT_3D('',#961,#962,#963);
#961 = CARTESIAN_POINT('',(200.,0.,30.));
#962 = DIRECTION('',(0.,1.,0.));
#963 = DIRECTION('',(0.,-0.,1.));
#964 = ADVANCED_FACE('',(#965),#976,.T.);
#965 = FACE_BOUND('',#966,.T.);
#966 = EDGE_LOOP('',(#967,#973,#974,#975));
#967 = ORIENTED_EDGE('',*,*,#968,.F.);
#968 = EDGE_CURVE('',#701,#526,#969,.T.);
#969 = LINE('',#970,#971);
#970 = CARTESIAN_POINT('',(200.,0.,35.));
#971 = VECTOR('',#972,1.);
#972 = DIRECTION('',(-0.,1.,0.));
#973 = ORIENTED_EDGE('',*,*,#708,.T.);
#974 = ORIENTED_EDGE('',*,*,#951,.T.);
#975 = ORIENTED_EDGE('',*,*,#533,.T.);
#976 = PLANE('',#977);
#977 = AXIS2_PLACEMENT_3D('',#978,#979,#980);
#978 = CARTESIAN_POINT('',(200.,0.,30.));
#979 = DIRECTION('',(1.,0.,-0.));
#980 = DIRECTION('',(0.,0.,1.));
#981 = ADVANCED_FACE('',(#982),#993,.T.);
#982 = FACE_BOUND('',#983,.T.);
#983 = EDGE_LOOP('',(#984,#990,#991,#992));
#984 = ORIENTED_EDGE('',*,*,#985,.F.);
#985 = EDGE_CURVE('',#693,#518,#986,.T.);
#986 = LINE('',#987,#988);
#987 = CARTESIAN_POINT('',(170.,0.,35.));
#988 = VECTOR('',#989,1.);
#989 = DIRECTION('',(-0.,1.,0.));
#990 = ORIENTED_EDGE('',*,*,#700,.T.);
#991 = ORIENTED_EDGE('',*,*,#968,.T.);
#992 = ORIENTED_EDGE('',*,*,#525,.F.);
#993 = PLANE('',#994);
#994 = AXIS2_PLACEMENT_3D('',#995,#996,#997);
#995 = CARTESIAN_POINT('',(120.,0.,35.));
#996 = DIRECTION('',(0.,0.,1.));
#997 = DIRECTION('',(1.,0.,-0.));
#998 = ADVANCED_FACE('',(#999),#1010,.F.);
#999 = FACE_BOUND('',#1000,.F.);
#1000 = EDGE_LOOP('',(#1001,#1002,#1003,#1009));
#1001 = ORIENTED_EDGE('',*,*,#985,.F.);
#1002 = ORIENTED_EDGE('',*,*,#692,.T.);
#1003 = ORIENTED_EDGE('',*,*,#1004,.T.);
#1004 = EDGE_CURVE('',#684,#509,#1005,.T.);
#1005 = LINE('',#1006,#1007);
#1006 = CARTESIAN_POINT('',(170.,0.,32.));
#1007 = VECTOR('',#1008,1.);
#1008 = DIRECTION('',(0.,1.,0.));
#1009 = ORIENTED_EDGE('',*,*,#517,.T.);
#1010 = PLANE('',#1011);
#1011 = AXIS2_PLACEMENT_3D('',#1012,#1013,#1014);
#1012 = CARTESIAN_POINT('',(170.,0.,30.));
#1013 = DIRECTION('',(1.,0.,-0.));
#1014 = DIRECTION('',(0.,0.,1.));
#1015 = ADVANCED_FACE('',(#1016),#1027,.F.);
#1016 = FACE_BOUND('',#1017,.F.);
#1017 = EDGE_LOOP('',(#1018,#1019,#1020,#1021));
#1018 = ORIENTED_EDGE('',*,*,#508,.F.);
#1019 = ORIENTED_EDGE('',*,*,#1004,.F.);
#1020 = ORIENTED_EDGE('',*,*,#683,.T.);
#1021 = ORIENTED_EDGE('',*,*,#1022,.T.);
#1022 = EDGE_CURVE('',#676,#501,#1023,.T.);
#1023 = LINE('',#1024,#1025);
#1024 = CARTESIAN_POINT('',(168.,0.,30.));
#1025 = VECTOR('',#1026,1.);
#1026 = DIRECTION('',(0.,1.,0.));
#1027 = CYLINDRICAL_SURFACE('',#1028,2.);
#1028 = AXIS2_PLACEMENT_3D('',#1029,#1030,#1031);
#1029 = CARTESIAN_POINT('',(170.,0.,30.));
#1030 = DIRECTION('',(0.,1.,0.));
#1031 = DIRECTION('',(0.,-0.,1.));
#1032 = ADVANCED_FACE('',(#1033),#1044,.T.);
#1033 = FACE_BOUND('',#1034,.T.);
#1034 = EDGE_LOOP('',(#1035,#1041,#1042,#1043));
#1035 = ORIENTED_EDGE('',*,*,#1036,.F.);
#1036 = EDGE_CURVE('',#668,#493,#1037,.T.);
#1037 = LINE('',#1038,#1039);
#1038 = CARTESIAN_POINT('',(120.,0.,30.));
#1039 = VECTOR('',#1040,1.);
#1040 = DIRECTION('',(-0.,1.,0.));
#1041 = ORIENTED_EDGE('',*,*,#675,.T.);
#1042 = ORIENTED_EDGE('',*,*,#1022,.T.);
#1043 = ORIENTED_EDGE('',*,*,#500,.F.);
#1044 = PLANE('',#1045);
#1045 = AXIS2_PLACEMENT_3D('',#1046,#1047,#1048);
#1046 = CARTESIAN_POINT('',(120.,0.,30.));
#1047 = DIRECTION('',(0.,0.,1.));
#1048 = DIRECTION('',(1.,0.,-0.));
#1049 = ADVANCED_FACE('',(#1050),#1061,.T.);
#1050 = FACE_BOUND('',#1051,.T.);
#1051 = EDGE_LOOP('',(#1052,#1053,#1054,#1055));
#1052 = ORIENTED_EDGE('',*,*,#667,.T.);
#1053 = ORIENTED_EDGE('',*,*,#1036,.T.);
#1054 = ORIENTED_EDGE('',*,*,#492,.T.);
#1055 = ORIENTED_EDGE('',*,*,#1056,.F.);
#1056 = EDGE_CURVE('',#660,#485,#1057,.T.);
#1057 = LINE('',#1058,#1059);
#1058 = CARTESIAN_POINT('',(120.,0.,35.));
#1059 = VECTOR('',#1060,1.);
#1060 = DIRECTION('',(-0.,1.,0.));
#1061 = PLANE('',#1062);
#1062 = AXIS2_PLACEMENT_3D('',#1063,#1064,#1065);
#1063 = CARTESIAN_POINT('',(120.,0.,30.));
#1064 = DIRECTION('',(1.,0.,-0.));
#1065 = DIRECTION('',(0.,0.,1.));
#1066 = ADVANCED_FACE('',(#1067),#1073,.T.);
#1067 = FACE_BOUND('',#1068,.T.);
#1068 = EDGE_LOOP('',(#1069,#1070,#1071,#1072));
#1069 = ORIENTED_EDGE('',*,*,#659,.T.);
#1070 = ORIENTED_EDGE('',*,*,#1056,.T.);
#1071 = ORIENTED_EDGE('',*,*,#484,.T.);
#1072 = ORIENTED_EDGE('',*,*,#608,.F.);
#1073 = PLANE('',#1074);
#1074 = AXIS2_PLACEMENT_3D('',#1075,#1076,#1077);
#1075 = CARTESIAN_POINT('',(120.,0.,35.));
#1076 = DIRECTION('',(1.,0.,-0.));
#1077 = DIRECTION('',(0.,0.,1.));
#1078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1082)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1079,#1080,#1081)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1079 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1080 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1081 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1082 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1079,
  'distance_accuracy_value','confusion accuracy');
#1083 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#1084 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1085),#1078);
#1085 = STYLED_ITEM('color',(#1086),#15);
#1086 = PRESENTATION_STYLE_ASSIGNMENT((#1087,#1093));
#1087 = SURFACE_STYLE_USAGE(.BOTH.,#1088);
#1088 = SURFACE_SIDE_STYLE('',(#1089));
#1089 = SURFACE_STYLE_FILL_AREA(#1090);
#1090 = FILL_AREA_STYLE('',(#1091));
#1091 = FILL_AREA_STYLE_COLOUR('',#1092);
#1092 = COLOUR_RGB('',0.447059003357,0.474510015008,0.501960993452);
#1093 = CURVE_STYLE('',#1094,POSITIVE_LENGTH_MEASURE(0.1),#1095);
#1094 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1095 = COLOUR_RGB('',9.803921802644E-02,9.803921802644E-02,
  9.803921802644E-02);
ENDSEC;
END-ISO-10303-21;
