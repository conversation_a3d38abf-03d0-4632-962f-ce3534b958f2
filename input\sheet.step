ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-08-20T09:14:02',('Author'),(
    ''),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Platine_200x100x3_R5','Platine_200x100x3_R5','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#305);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#124,#149,#174,#225,#242,#259,#276,#293)
  );
#17 = ADVANCED_FACE('',(#18),#52,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(-8.881784197001E-16,5.,3.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(-8.881784197001E-16,95.,3.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,0.,3.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(-0.,1.,0.));
#30 = ORIENTED_EDGE('',*,*,#31,.F.);
#31 = EDGE_CURVE('',#32,#22,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(-8.881784197001E-16,5.,0.));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(-8.881784197001E-16,5.,0.));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(0.,0.,1.));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(-8.881784197001E-16,95.,0.));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(0.,0.,0.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(-0.,1.,0.));
#46 = ORIENTED_EDGE('',*,*,#47,.T.);
#47 = EDGE_CURVE('',#40,#24,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(-8.881784197001E-16,95.,0.));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(0.,0.,1.));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(0.,0.,0.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = DIRECTION('',(0.,0.,1.));
#57 = ADVANCED_FACE('',(#58),#119,.T.);
#58 = FACE_BOUND('',#59,.T.);
#59 = EDGE_LOOP('',(#60,#61,#70,#78,#87,#95,#104,#112));
#60 = ORIENTED_EDGE('',*,*,#21,.F.);
#61 = ORIENTED_EDGE('',*,*,#62,.T.);
#62 = EDGE_CURVE('',#22,#63,#65,.T.);
#63 = VERTEX_POINT('',#64);
#64 = CARTESIAN_POINT('',(5.,-8.881784197001E-16,3.));
#65 = CIRCLE('',#66,5.);
#66 = AXIS2_PLACEMENT_3D('',#67,#68,#69);
#67 = CARTESIAN_POINT('',(5.,5.,3.));
#68 = DIRECTION('',(0.,0.,1.));
#69 = DIRECTION('',(0.,-1.,0.));
#70 = ORIENTED_EDGE('',*,*,#71,.T.);
#71 = EDGE_CURVE('',#63,#72,#74,.T.);
#72 = VERTEX_POINT('',#73);
#73 = CARTESIAN_POINT('',(195.,-8.881784197001E-16,3.));
#74 = LINE('',#75,#76);
#75 = CARTESIAN_POINT('',(0.,0.,3.));
#76 = VECTOR('',#77,1.);
#77 = DIRECTION('',(1.,0.,-0.));
#78 = ORIENTED_EDGE('',*,*,#79,.F.);
#79 = EDGE_CURVE('',#80,#72,#82,.T.);
#80 = VERTEX_POINT('',#81);
#81 = CARTESIAN_POINT('',(200.,5.,3.));
#82 = CIRCLE('',#83,5.);
#83 = AXIS2_PLACEMENT_3D('',#84,#85,#86);
#84 = CARTESIAN_POINT('',(195.,5.,3.));
#85 = DIRECTION('',(-0.,-0.,-1.));
#86 = DIRECTION('',(0.,-1.,0.));
#87 = ORIENTED_EDGE('',*,*,#88,.T.);
#88 = EDGE_CURVE('',#80,#89,#91,.T.);
#89 = VERTEX_POINT('',#90);
#90 = CARTESIAN_POINT('',(200.,95.,3.));
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(200.,0.,3.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(-0.,1.,0.));
#95 = ORIENTED_EDGE('',*,*,#96,.T.);
#96 = EDGE_CURVE('',#89,#97,#99,.T.);
#97 = VERTEX_POINT('',#98);
#98 = CARTESIAN_POINT('',(195.,100.,3.));
#99 = CIRCLE('',#100,5.);
#100 = AXIS2_PLACEMENT_3D('',#101,#102,#103);
#101 = CARTESIAN_POINT('',(195.,95.,3.));
#102 = DIRECTION('',(0.,0.,1.));
#103 = DIRECTION('',(0.,-1.,0.));
#104 = ORIENTED_EDGE('',*,*,#105,.F.);
#105 = EDGE_CURVE('',#106,#97,#108,.T.);
#106 = VERTEX_POINT('',#107);
#107 = CARTESIAN_POINT('',(5.,100.,3.));
#108 = LINE('',#109,#110);
#109 = CARTESIAN_POINT('',(0.,100.,3.));
#110 = VECTOR('',#111,1.);
#111 = DIRECTION('',(1.,0.,-0.));
#112 = ORIENTED_EDGE('',*,*,#113,.F.);
#113 = EDGE_CURVE('',#24,#106,#114,.T.);
#114 = CIRCLE('',#115,5.);
#115 = AXIS2_PLACEMENT_3D('',#116,#117,#118);
#116 = CARTESIAN_POINT('',(5.,95.,3.));
#117 = DIRECTION('',(-0.,-0.,-1.));
#118 = DIRECTION('',(0.,-1.,0.));
#119 = PLANE('',#120);
#120 = AXIS2_PLACEMENT_3D('',#121,#122,#123);
#121 = CARTESIAN_POINT('',(0.,0.,3.));
#122 = DIRECTION('',(0.,0.,1.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = ADVANCED_FACE('',(#125),#144,.T.);
#125 = FACE_BOUND('',#126,.T.);
#126 = EDGE_LOOP('',(#127,#136,#142,#143));
#127 = ORIENTED_EDGE('',*,*,#128,.T.);
#128 = EDGE_CURVE('',#32,#129,#131,.T.);
#129 = VERTEX_POINT('',#130);
#130 = CARTESIAN_POINT('',(5.,-8.881784197001E-16,0.));
#131 = CIRCLE('',#132,5.);
#132 = AXIS2_PLACEMENT_3D('',#133,#134,#135);
#133 = CARTESIAN_POINT('',(5.,5.,0.));
#134 = DIRECTION('',(0.,0.,1.));
#135 = DIRECTION('',(0.,-1.,0.));
#136 = ORIENTED_EDGE('',*,*,#137,.T.);
#137 = EDGE_CURVE('',#129,#63,#138,.T.);
#138 = LINE('',#139,#140);
#139 = CARTESIAN_POINT('',(5.,-8.881784197001E-16,0.));
#140 = VECTOR('',#141,1.);
#141 = DIRECTION('',(0.,0.,1.));
#142 = ORIENTED_EDGE('',*,*,#62,.F.);
#143 = ORIENTED_EDGE('',*,*,#31,.F.);
#144 = CYLINDRICAL_SURFACE('',#145,5.);
#145 = AXIS2_PLACEMENT_3D('',#146,#147,#148);
#146 = CARTESIAN_POINT('',(5.,5.,0.));
#147 = DIRECTION('',(0.,0.,1.));
#148 = DIRECTION('',(-1.,-0.,0.));
#149 = ADVANCED_FACE('',(#150),#169,.T.);
#150 = FACE_BOUND('',#151,.F.);
#151 = EDGE_LOOP('',(#152,#161,#167,#168));
#152 = ORIENTED_EDGE('',*,*,#153,.T.);
#153 = EDGE_CURVE('',#40,#154,#156,.T.);
#154 = VERTEX_POINT('',#155);
#155 = CARTESIAN_POINT('',(5.,100.,0.));
#156 = CIRCLE('',#157,5.);
#157 = AXIS2_PLACEMENT_3D('',#158,#159,#160);
#158 = CARTESIAN_POINT('',(5.,95.,0.));
#159 = DIRECTION('',(-0.,-0.,-1.));
#160 = DIRECTION('',(0.,-1.,0.));
#161 = ORIENTED_EDGE('',*,*,#162,.T.);
#162 = EDGE_CURVE('',#154,#106,#163,.T.);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(5.,100.,0.));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(0.,0.,1.));
#167 = ORIENTED_EDGE('',*,*,#113,.F.);
#168 = ORIENTED_EDGE('',*,*,#47,.F.);
#169 = CYLINDRICAL_SURFACE('',#170,5.);
#170 = AXIS2_PLACEMENT_3D('',#171,#172,#173);
#171 = CARTESIAN_POINT('',(5.,95.,0.));
#172 = DIRECTION('',(0.,0.,1.));
#173 = DIRECTION('',(-1.,-0.,0.));
#174 = ADVANCED_FACE('',(#175),#220,.F.);
#175 = FACE_BOUND('',#176,.F.);
#176 = EDGE_LOOP('',(#177,#178,#179,#187,#196,#204,#213,#219));
#177 = ORIENTED_EDGE('',*,*,#39,.F.);
#178 = ORIENTED_EDGE('',*,*,#128,.T.);
#179 = ORIENTED_EDGE('',*,*,#180,.T.);
#180 = EDGE_CURVE('',#129,#181,#183,.T.);
#181 = VERTEX_POINT('',#182);
#182 = CARTESIAN_POINT('',(195.,-8.881784197001E-16,0.));
#183 = LINE('',#184,#185);
#184 = CARTESIAN_POINT('',(0.,0.,0.));
#185 = VECTOR('',#186,1.);
#186 = DIRECTION('',(1.,0.,-0.));
#187 = ORIENTED_EDGE('',*,*,#188,.F.);
#188 = EDGE_CURVE('',#189,#181,#191,.T.);
#189 = VERTEX_POINT('',#190);
#190 = CARTESIAN_POINT('',(200.,5.,0.));
#191 = CIRCLE('',#192,5.);
#192 = AXIS2_PLACEMENT_3D('',#193,#194,#195);
#193 = CARTESIAN_POINT('',(195.,5.,0.));
#194 = DIRECTION('',(-0.,-0.,-1.));
#195 = DIRECTION('',(0.,-1.,0.));
#196 = ORIENTED_EDGE('',*,*,#197,.T.);
#197 = EDGE_CURVE('',#189,#198,#200,.T.);
#198 = VERTEX_POINT('',#199);
#199 = CARTESIAN_POINT('',(200.,95.,0.));
#200 = LINE('',#201,#202);
#201 = CARTESIAN_POINT('',(200.,0.,0.));
#202 = VECTOR('',#203,1.);
#203 = DIRECTION('',(-0.,1.,0.));
#204 = ORIENTED_EDGE('',*,*,#205,.T.);
#205 = EDGE_CURVE('',#198,#206,#208,.T.);
#206 = VERTEX_POINT('',#207);
#207 = CARTESIAN_POINT('',(195.,100.,0.));
#208 = CIRCLE('',#209,5.);
#209 = AXIS2_PLACEMENT_3D('',#210,#211,#212);
#210 = CARTESIAN_POINT('',(195.,95.,0.));
#211 = DIRECTION('',(0.,0.,1.));
#212 = DIRECTION('',(0.,-1.,0.));
#213 = ORIENTED_EDGE('',*,*,#214,.F.);
#214 = EDGE_CURVE('',#154,#206,#215,.T.);
#215 = LINE('',#216,#217);
#216 = CARTESIAN_POINT('',(0.,100.,0.));
#217 = VECTOR('',#218,1.);
#218 = DIRECTION('',(1.,0.,-0.));
#219 = ORIENTED_EDGE('',*,*,#153,.F.);
#220 = PLANE('',#221);
#221 = AXIS2_PLACEMENT_3D('',#222,#223,#224);
#222 = CARTESIAN_POINT('',(0.,0.,0.));
#223 = DIRECTION('',(0.,0.,1.));
#224 = DIRECTION('',(1.,0.,-0.));
#225 = ADVANCED_FACE('',(#226),#237,.F.);
#226 = FACE_BOUND('',#227,.F.);
#227 = EDGE_LOOP('',(#228,#229,#230,#231));
#228 = ORIENTED_EDGE('',*,*,#180,.F.);
#229 = ORIENTED_EDGE('',*,*,#137,.T.);
#230 = ORIENTED_EDGE('',*,*,#71,.T.);
#231 = ORIENTED_EDGE('',*,*,#232,.F.);
#232 = EDGE_CURVE('',#181,#72,#233,.T.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(195.,-8.881784197001E-16,0.));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.,0.,1.));
#237 = PLANE('',#238);
#238 = AXIS2_PLACEMENT_3D('',#239,#240,#241);
#239 = CARTESIAN_POINT('',(0.,0.,0.));
#240 = DIRECTION('',(-0.,1.,0.));
#241 = DIRECTION('',(0.,0.,1.));
#242 = ADVANCED_FACE('',(#243),#254,.T.);
#243 = FACE_BOUND('',#244,.T.);
#244 = EDGE_LOOP('',(#245,#246,#247,#248));
#245 = ORIENTED_EDGE('',*,*,#214,.F.);
#246 = ORIENTED_EDGE('',*,*,#162,.T.);
#247 = ORIENTED_EDGE('',*,*,#105,.T.);
#248 = ORIENTED_EDGE('',*,*,#249,.F.);
#249 = EDGE_CURVE('',#206,#97,#250,.T.);
#250 = LINE('',#251,#252);
#251 = CARTESIAN_POINT('',(195.,100.,0.));
#252 = VECTOR('',#253,1.);
#253 = DIRECTION('',(0.,0.,1.));
#254 = PLANE('',#255);
#255 = AXIS2_PLACEMENT_3D('',#256,#257,#258);
#256 = CARTESIAN_POINT('',(0.,100.,0.));
#257 = DIRECTION('',(-0.,1.,0.));
#258 = DIRECTION('',(0.,0.,1.));
#259 = ADVANCED_FACE('',(#260),#271,.T.);
#260 = FACE_BOUND('',#261,.F.);
#261 = EDGE_LOOP('',(#262,#263,#264,#265));
#262 = ORIENTED_EDGE('',*,*,#188,.T.);
#263 = ORIENTED_EDGE('',*,*,#232,.T.);
#264 = ORIENTED_EDGE('',*,*,#79,.F.);
#265 = ORIENTED_EDGE('',*,*,#266,.F.);
#266 = EDGE_CURVE('',#189,#80,#267,.T.);
#267 = LINE('',#268,#269);
#268 = CARTESIAN_POINT('',(200.,5.,0.));
#269 = VECTOR('',#270,1.);
#270 = DIRECTION('',(0.,0.,1.));
#271 = CYLINDRICAL_SURFACE('',#272,5.);
#272 = AXIS2_PLACEMENT_3D('',#273,#274,#275);
#273 = CARTESIAN_POINT('',(195.,5.,0.));
#274 = DIRECTION('',(0.,0.,1.));
#275 = DIRECTION('',(1.,0.,-0.));
#276 = ADVANCED_FACE('',(#277),#288,.T.);
#277 = FACE_BOUND('',#278,.T.);
#278 = EDGE_LOOP('',(#279,#280,#281,#282));
#279 = ORIENTED_EDGE('',*,*,#205,.T.);
#280 = ORIENTED_EDGE('',*,*,#249,.T.);
#281 = ORIENTED_EDGE('',*,*,#96,.F.);
#282 = ORIENTED_EDGE('',*,*,#283,.F.);
#283 = EDGE_CURVE('',#198,#89,#284,.T.);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(200.,95.,0.));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(0.,0.,1.));
#288 = CYLINDRICAL_SURFACE('',#289,5.);
#289 = AXIS2_PLACEMENT_3D('',#290,#291,#292);
#290 = CARTESIAN_POINT('',(195.,95.,0.));
#291 = DIRECTION('',(0.,0.,1.));
#292 = DIRECTION('',(1.,0.,-0.));
#293 = ADVANCED_FACE('',(#294),#300,.T.);
#294 = FACE_BOUND('',#295,.T.);
#295 = EDGE_LOOP('',(#296,#297,#298,#299));
#296 = ORIENTED_EDGE('',*,*,#88,.F.);
#297 = ORIENTED_EDGE('',*,*,#266,.F.);
#298 = ORIENTED_EDGE('',*,*,#197,.T.);
#299 = ORIENTED_EDGE('',*,*,#283,.T.);
#300 = PLANE('',#301);
#301 = AXIS2_PLACEMENT_3D('',#302,#303,#304);
#302 = CARTESIAN_POINT('',(200.,0.,0.));
#303 = DIRECTION('',(1.,0.,-0.));
#304 = DIRECTION('',(0.,0.,1.));
#305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#309)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#306,#307,#308)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#306 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#307 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#308 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#309 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#306,
  'distance_accuracy_value','confusion accuracy');
#310 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
