ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-07-02T13:48:02',('Author'),(
    ''),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('NCTru','NCTru','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#747);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#125,#156,#198,#222,#284,#308,#350,#392,#416,
    #478,#502,#544,#561,#580,#597,#614,#636,#653,#665,#682,#699,#716,
    #733));
#17 = ADVANCED_FACE('',(#18),#120,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#39,#47,#56,#64,#72,#80,#89,#97,#106,#114));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,70.,0.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,70.,25.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,70.,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#24,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(0.,60.,35.));
#34 = CIRCLE('',#35,10.);
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(0.,60.,25.));
#37 = DIRECTION('',(1.,0.,-0.));
#38 = DIRECTION('',(0.,0.,1.));
#39 = ORIENTED_EDGE('',*,*,#40,.F.);
#40 = EDGE_CURVE('',#41,#32,#43,.T.);
#41 = VERTEX_POINT('',#42);
#42 = CARTESIAN_POINT('',(0.,10.,35.));
#43 = LINE('',#44,#45);
#44 = CARTESIAN_POINT('',(0.,0.,35.));
#45 = VECTOR('',#46,1.);
#46 = DIRECTION('',(-0.,1.,0.));
#47 = ORIENTED_EDGE('',*,*,#48,.T.);
#48 = EDGE_CURVE('',#41,#49,#51,.T.);
#49 = VERTEX_POINT('',#50);
#50 = CARTESIAN_POINT('',(0.,-1.7763568394E-15,25.));
#51 = CIRCLE('',#52,10.);
#52 = AXIS2_PLACEMENT_3D('',#53,#54,#55);
#53 = CARTESIAN_POINT('',(0.,10.,25.));
#54 = DIRECTION('',(1.,0.,-0.));
#55 = DIRECTION('',(0.,0.,1.));
#56 = ORIENTED_EDGE('',*,*,#57,.F.);
#57 = EDGE_CURVE('',#58,#49,#60,.T.);
#58 = VERTEX_POINT('',#59);
#59 = CARTESIAN_POINT('',(0.,0.,0.));
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(0.,0.,0.));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(0.,0.,1.));
#64 = ORIENTED_EDGE('',*,*,#65,.T.);
#65 = EDGE_CURVE('',#58,#66,#68,.T.);
#66 = VERTEX_POINT('',#67);
#67 = CARTESIAN_POINT('',(0.,5.,0.));
#68 = LINE('',#69,#70);
#69 = CARTESIAN_POINT('',(0.,0.,0.));
#70 = VECTOR('',#71,1.);
#71 = DIRECTION('',(-0.,1.,0.));
#72 = ORIENTED_EDGE('',*,*,#73,.T.);
#73 = EDGE_CURVE('',#66,#74,#76,.T.);
#74 = VERTEX_POINT('',#75);
#75 = CARTESIAN_POINT('',(0.,5.,25.));
#76 = LINE('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,5.,0.));
#78 = VECTOR('',#79,1.);
#79 = DIRECTION('',(0.,0.,1.));
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#82,#74,#84,.T.);
#82 = VERTEX_POINT('',#83);
#83 = CARTESIAN_POINT('',(0.,10.,30.));
#84 = CIRCLE('',#85,5.);
#85 = AXIS2_PLACEMENT_3D('',#86,#87,#88);
#86 = CARTESIAN_POINT('',(0.,10.,25.));
#87 = DIRECTION('',(1.,0.,-0.));
#88 = DIRECTION('',(0.,0.,1.));
#89 = ORIENTED_EDGE('',*,*,#90,.T.);
#90 = EDGE_CURVE('',#82,#91,#93,.T.);
#91 = VERTEX_POINT('',#92);
#92 = CARTESIAN_POINT('',(0.,60.,30.));
#93 = LINE('',#94,#95);
#94 = CARTESIAN_POINT('',(0.,5.,30.));
#95 = VECTOR('',#96,1.);
#96 = DIRECTION('',(-0.,1.,0.));
#97 = ORIENTED_EDGE('',*,*,#98,.F.);
#98 = EDGE_CURVE('',#99,#91,#101,.T.);
#99 = VERTEX_POINT('',#100);
#100 = CARTESIAN_POINT('',(0.,65.,25.));
#101 = CIRCLE('',#102,5.);
#102 = AXIS2_PLACEMENT_3D('',#103,#104,#105);
#103 = CARTESIAN_POINT('',(0.,60.,25.));
#104 = DIRECTION('',(1.,0.,-0.));
#105 = DIRECTION('',(0.,0.,1.));
#106 = ORIENTED_EDGE('',*,*,#107,.F.);
#107 = EDGE_CURVE('',#108,#99,#110,.T.);
#108 = VERTEX_POINT('',#109);
#109 = CARTESIAN_POINT('',(0.,65.,0.));
#110 = LINE('',#111,#112);
#111 = CARTESIAN_POINT('',(0.,65.,0.));
#112 = VECTOR('',#113,1.);
#113 = DIRECTION('',(0.,0.,1.));
#114 = ORIENTED_EDGE('',*,*,#115,.T.);
#115 = EDGE_CURVE('',#108,#22,#116,.T.);
#116 = LINE('',#117,#118);
#117 = CARTESIAN_POINT('',(0.,0.,0.));
#118 = VECTOR('',#119,1.);
#119 = DIRECTION('',(-0.,1.,0.));
#120 = PLANE('',#121);
#121 = AXIS2_PLACEMENT_3D('',#122,#123,#124);
#122 = CARTESIAN_POINT('',(0.,0.,0.));
#123 = DIRECTION('',(1.,0.,-0.));
#124 = DIRECTION('',(0.,0.,1.));
#125 = ADVANCED_FACE('',(#126),#151,.T.);
#126 = FACE_BOUND('',#127,.T.);
#127 = EDGE_LOOP('',(#128,#138,#144,#145));
#128 = ORIENTED_EDGE('',*,*,#129,.F.);
#129 = EDGE_CURVE('',#130,#132,#134,.T.);
#130 = VERTEX_POINT('',#131);
#131 = CARTESIAN_POINT('',(230.,70.,0.));
#132 = VERTEX_POINT('',#133);
#133 = CARTESIAN_POINT('',(230.,70.,25.));
#134 = LINE('',#135,#136);
#135 = CARTESIAN_POINT('',(230.,70.,0.));
#136 = VECTOR('',#137,1.);
#137 = DIRECTION('',(0.,0.,1.));
#138 = ORIENTED_EDGE('',*,*,#139,.F.);
#139 = EDGE_CURVE('',#22,#130,#140,.T.);
#140 = LINE('',#141,#142);
#141 = CARTESIAN_POINT('',(0.,70.,0.));
#142 = VECTOR('',#143,1.);
#143 = DIRECTION('',(1.,0.,-0.));
#144 = ORIENTED_EDGE('',*,*,#21,.T.);
#145 = ORIENTED_EDGE('',*,*,#146,.T.);
#146 = EDGE_CURVE('',#24,#132,#147,.T.);
#147 = LINE('',#148,#149);
#148 = CARTESIAN_POINT('',(0.,70.,25.));
#149 = VECTOR('',#150,1.);
#150 = DIRECTION('',(1.,0.,-0.));
#151 = PLANE('',#152);
#152 = AXIS2_PLACEMENT_3D('',#153,#154,#155);
#153 = CARTESIAN_POINT('',(0.,70.,0.));
#154 = DIRECTION('',(-0.,1.,0.));
#155 = DIRECTION('',(0.,0.,1.));
#156 = ADVANCED_FACE('',(#157),#193,.T.);
#157 = FACE_BOUND('',#158,.T.);
#158 = EDGE_LOOP('',(#159,#168,#169,#170,#178,#187));
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#132,#161,#163,.T.);
#161 = VERTEX_POINT('',#162);
#162 = CARTESIAN_POINT('',(230.,69.539392014169,28.));
#163 = CIRCLE('',#164,10.);
#164 = AXIS2_PLACEMENT_3D('',#165,#166,#167);
#165 = CARTESIAN_POINT('',(230.,60.,25.));
#166 = DIRECTION('',(1.,0.,-0.));
#167 = DIRECTION('',(0.,0.,1.));
#168 = ORIENTED_EDGE('',*,*,#146,.F.);
#169 = ORIENTED_EDGE('',*,*,#31,.T.);
#170 = ORIENTED_EDGE('',*,*,#171,.T.);
#171 = EDGE_CURVE('',#32,#172,#174,.T.);
#172 = VERTEX_POINT('',#173);
#173 = CARTESIAN_POINT('',(228.,60.,35.));
#174 = LINE('',#175,#176);
#175 = CARTESIAN_POINT('',(0.,60.,35.));
#176 = VECTOR('',#177,1.);
#177 = DIRECTION('',(1.,0.,-0.));
#178 = ORIENTED_EDGE('',*,*,#179,.F.);
#179 = EDGE_CURVE('',#180,#172,#182,.T.);
#180 = VERTEX_POINT('',#181);
#181 = CARTESIAN_POINT('',(228.,69.539392014169,28.));
#182 = CIRCLE('',#183,10.);
#183 = AXIS2_PLACEMENT_3D('',#184,#185,#186);
#184 = CARTESIAN_POINT('',(228.,60.,25.));
#185 = DIRECTION('',(1.,0.,-0.));
#186 = DIRECTION('',(0.,0.,1.));
#187 = ORIENTED_EDGE('',*,*,#188,.F.);
#188 = EDGE_CURVE('',#161,#180,#189,.T.);
#189 = LINE('',#190,#191);
#190 = CARTESIAN_POINT('',(0.,69.539392014169,28.));
#191 = VECTOR('',#192,1.);
#192 = DIRECTION('',(-1.,-0.,0.));
#193 = CYLINDRICAL_SURFACE('',#194,10.);
#194 = AXIS2_PLACEMENT_3D('',#195,#196,#197);
#195 = CARTESIAN_POINT('',(0.,60.,25.));
#196 = DIRECTION('',(1.,0.,-0.));
#197 = DIRECTION('',(0.,1.,0.));
#198 = ADVANCED_FACE('',(#199),#217,.F.);
#199 = FACE_BOUND('',#200,.F.);
#200 = EDGE_LOOP('',(#201,#202,#210,#216));
#201 = ORIENTED_EDGE('',*,*,#115,.F.);
#202 = ORIENTED_EDGE('',*,*,#203,.T.);
#203 = EDGE_CURVE('',#108,#204,#206,.T.);
#204 = VERTEX_POINT('',#205);
#205 = CARTESIAN_POINT('',(230.,65.,0.));
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.,65.,0.));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.,-0.));
#210 = ORIENTED_EDGE('',*,*,#211,.T.);
#211 = EDGE_CURVE('',#204,#130,#212,.T.);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(230.,0.,0.));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(-0.,1.,0.));
#216 = ORIENTED_EDGE('',*,*,#139,.F.);
#217 = PLANE('',#218);
#218 = AXIS2_PLACEMENT_3D('',#219,#220,#221);
#219 = CARTESIAN_POINT('',(0.,0.,0.));
#220 = DIRECTION('',(0.,0.,1.));
#221 = DIRECTION('',(1.,0.,-0.));
#222 = ADVANCED_FACE('',(#223,#257,#268),#279,.T.);
#223 = FACE_BOUND('',#224,.T.);
#224 = EDGE_LOOP('',(#225,#226,#234,#242,#250,#256));
#225 = ORIENTED_EDGE('',*,*,#40,.F.);
#226 = ORIENTED_EDGE('',*,*,#227,.T.);
#227 = EDGE_CURVE('',#41,#228,#230,.T.);
#228 = VERTEX_POINT('',#229);
#229 = CARTESIAN_POINT('',(228.,10.,35.));
#230 = LINE('',#231,#232);
#231 = CARTESIAN_POINT('',(0.,10.,35.));
#232 = VECTOR('',#233,1.);
#233 = DIRECTION('',(1.,0.,-0.));
#234 = ORIENTED_EDGE('',*,*,#235,.T.);
#235 = EDGE_CURVE('',#228,#236,#238,.T.);
#236 = VERTEX_POINT('',#237);
#237 = CARTESIAN_POINT('',(230.,10.,35.));
#238 = LINE('',#239,#240);
#239 = CARTESIAN_POINT('',(228.,10.,35.));
#240 = VECTOR('',#241,1.);
#241 = DIRECTION('',(1.,0.,-0.));
#242 = ORIENTED_EDGE('',*,*,#243,.T.);
#243 = EDGE_CURVE('',#236,#244,#246,.T.);
#244 = VERTEX_POINT('',#245);
#245 = CARTESIAN_POINT('',(230.,60.,35.));
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(230.,0.,35.));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(-0.,1.,0.));
#250 = ORIENTED_EDGE('',*,*,#251,.F.);
#251 = EDGE_CURVE('',#172,#244,#252,.T.);
#252 = LINE('',#253,#254);
#253 = CARTESIAN_POINT('',(228.,60.,35.));
#254 = VECTOR('',#255,1.);
#255 = DIRECTION('',(1.,0.,-0.));
#256 = ORIENTED_EDGE('',*,*,#171,.F.);
#257 = FACE_BOUND('',#258,.T.);
#258 = EDGE_LOOP('',(#259));
#259 = ORIENTED_EDGE('',*,*,#260,.F.);
#260 = EDGE_CURVE('',#261,#261,#263,.T.);
#261 = VERTEX_POINT('',#262);
#262 = CARTESIAN_POINT('',(51.,35.,35.));
#263 = CIRCLE('',#264,11.);
#264 = AXIS2_PLACEMENT_3D('',#265,#266,#267);
#265 = CARTESIAN_POINT('',(40.,35.,35.));
#266 = DIRECTION('',(0.,0.,1.));
#267 = DIRECTION('',(1.,0.,-0.));
#268 = FACE_BOUND('',#269,.T.);
#269 = EDGE_LOOP('',(#270));
#270 = ORIENTED_EDGE('',*,*,#271,.F.);
#271 = EDGE_CURVE('',#272,#272,#274,.T.);
#272 = VERTEX_POINT('',#273);
#273 = CARTESIAN_POINT('',(161.,35.,35.));
#274 = CIRCLE('',#275,11.);
#275 = AXIS2_PLACEMENT_3D('',#276,#277,#278);
#276 = CARTESIAN_POINT('',(150.,35.,35.));
#277 = DIRECTION('',(0.,0.,1.));
#278 = DIRECTION('',(1.,0.,-0.));
#279 = PLANE('',#280);
#280 = AXIS2_PLACEMENT_3D('',#281,#282,#283);
#281 = CARTESIAN_POINT('',(0.,0.,35.));
#282 = DIRECTION('',(0.,0.,1.));
#283 = DIRECTION('',(1.,0.,-0.));
#284 = ADVANCED_FACE('',(#285),#303,.F.);
#285 = FACE_BOUND('',#286,.F.);
#286 = EDGE_LOOP('',(#287,#295,#296,#297));
#287 = ORIENTED_EDGE('',*,*,#288,.F.);
#288 = EDGE_CURVE('',#204,#289,#291,.T.);
#289 = VERTEX_POINT('',#290);
#290 = CARTESIAN_POINT('',(230.,65.,25.));
#291 = LINE('',#292,#293);
#292 = CARTESIAN_POINT('',(230.,65.,0.));
#293 = VECTOR('',#294,1.);
#294 = DIRECTION('',(0.,0.,1.));
#295 = ORIENTED_EDGE('',*,*,#203,.F.);
#296 = ORIENTED_EDGE('',*,*,#107,.T.);
#297 = ORIENTED_EDGE('',*,*,#298,.T.);
#298 = EDGE_CURVE('',#99,#289,#299,.T.);
#299 = LINE('',#300,#301);
#300 = CARTESIAN_POINT('',(0.,65.,25.));
#301 = VECTOR('',#302,1.);
#302 = DIRECTION('',(1.,0.,-0.));
#303 = PLANE('',#304);
#304 = AXIS2_PLACEMENT_3D('',#305,#306,#307);
#305 = CARTESIAN_POINT('',(0.,65.,0.));
#306 = DIRECTION('',(-0.,1.,0.));
#307 = DIRECTION('',(0.,0.,1.));
#308 = ADVANCED_FACE('',(#309),#345,.T.);
#309 = FACE_BOUND('',#310,.T.);
#310 = EDGE_LOOP('',(#311,#320,#321,#322,#330,#339));
#311 = ORIENTED_EDGE('',*,*,#312,.F.);
#312 = EDGE_CURVE('',#228,#313,#315,.T.);
#313 = VERTEX_POINT('',#314);
#314 = CARTESIAN_POINT('',(228.,0.460607985831,28.));
#315 = CIRCLE('',#316,10.);
#316 = AXIS2_PLACEMENT_3D('',#317,#318,#319);
#317 = CARTESIAN_POINT('',(228.,10.,25.));
#318 = DIRECTION('',(1.,0.,-0.));
#319 = DIRECTION('',(0.,0.,1.));
#320 = ORIENTED_EDGE('',*,*,#227,.F.);
#321 = ORIENTED_EDGE('',*,*,#48,.T.);
#322 = ORIENTED_EDGE('',*,*,#323,.T.);
#323 = EDGE_CURVE('',#49,#324,#326,.T.);
#324 = VERTEX_POINT('',#325);
#325 = CARTESIAN_POINT('',(230.,-1.7763568394E-15,25.));
#326 = LINE('',#327,#328);
#327 = CARTESIAN_POINT('',(0.,-1.7763568394E-15,25.));
#328 = VECTOR('',#329,1.);
#329 = DIRECTION('',(1.,0.,-0.));
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#332,#324,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(230.,0.460607985831,28.));
#334 = CIRCLE('',#335,10.);
#335 = AXIS2_PLACEMENT_3D('',#336,#337,#338);
#336 = CARTESIAN_POINT('',(230.,10.,25.));
#337 = DIRECTION('',(1.,0.,-0.));
#338 = DIRECTION('',(0.,0.,1.));
#339 = ORIENTED_EDGE('',*,*,#340,.F.);
#340 = EDGE_CURVE('',#313,#332,#341,.T.);
#341 = LINE('',#342,#343);
#342 = CARTESIAN_POINT('',(0.,0.460607985831,28.));
#343 = VECTOR('',#344,1.);
#344 = DIRECTION('',(1.,0.,-0.));
#345 = CYLINDRICAL_SURFACE('',#346,10.);
#346 = AXIS2_PLACEMENT_3D('',#347,#348,#349);
#347 = CARTESIAN_POINT('',(0.,10.,25.));
#348 = DIRECTION('',(1.,0.,-0.));
#349 = DIRECTION('',(0.,0.,1.));
#350 = ADVANCED_FACE('',(#351),#387,.F.);
#351 = FACE_BOUND('',#352,.F.);
#352 = EDGE_LOOP('',(#353,#362,#363,#364,#372,#381));
#353 = ORIENTED_EDGE('',*,*,#354,.F.);
#354 = EDGE_CURVE('',#289,#355,#357,.T.);
#355 = VERTEX_POINT('',#356);
#356 = CARTESIAN_POINT('',(230.,64.,28.));
#357 = CIRCLE('',#358,5.);
#358 = AXIS2_PLACEMENT_3D('',#359,#360,#361);
#359 = CARTESIAN_POINT('',(230.,60.,25.));
#360 = DIRECTION('',(1.,0.,-0.));
#361 = DIRECTION('',(0.,0.,1.));
#362 = ORIENTED_EDGE('',*,*,#298,.F.);
#363 = ORIENTED_EDGE('',*,*,#98,.T.);
#364 = ORIENTED_EDGE('',*,*,#365,.T.);
#365 = EDGE_CURVE('',#91,#366,#368,.T.);
#366 = VERTEX_POINT('',#367);
#367 = CARTESIAN_POINT('',(228.,60.,30.));
#368 = LINE('',#369,#370);
#369 = CARTESIAN_POINT('',(0.,60.,30.));
#370 = VECTOR('',#371,1.);
#371 = DIRECTION('',(1.,0.,-0.));
#372 = ORIENTED_EDGE('',*,*,#373,.F.);
#373 = EDGE_CURVE('',#374,#366,#376,.T.);
#374 = VERTEX_POINT('',#375);
#375 = CARTESIAN_POINT('',(228.,64.,28.));
#376 = CIRCLE('',#377,5.);
#377 = AXIS2_PLACEMENT_3D('',#378,#379,#380);
#378 = CARTESIAN_POINT('',(228.,60.,25.));
#379 = DIRECTION('',(1.,0.,-0.));
#380 = DIRECTION('',(0.,0.,1.));
#381 = ORIENTED_EDGE('',*,*,#382,.F.);
#382 = EDGE_CURVE('',#355,#374,#383,.T.);
#383 = LINE('',#384,#385);
#384 = CARTESIAN_POINT('',(0.,64.,28.));
#385 = VECTOR('',#386,1.);
#386 = DIRECTION('',(-1.,-0.,0.));
#387 = CYLINDRICAL_SURFACE('',#388,5.);
#388 = AXIS2_PLACEMENT_3D('',#389,#390,#391);
#389 = CARTESIAN_POINT('',(0.,60.,25.));
#390 = DIRECTION('',(1.,0.,-0.));
#391 = DIRECTION('',(0.,1.,0.));
#392 = ADVANCED_FACE('',(#393),#411,.F.);
#393 = FACE_BOUND('',#394,.F.);
#394 = EDGE_LOOP('',(#395,#403,#409,#410));
#395 = ORIENTED_EDGE('',*,*,#396,.F.);
#396 = EDGE_CURVE('',#397,#324,#399,.T.);
#397 = VERTEX_POINT('',#398);
#398 = CARTESIAN_POINT('',(230.,0.,0.));
#399 = LINE('',#400,#401);
#400 = CARTESIAN_POINT('',(230.,0.,0.));
#401 = VECTOR('',#402,1.);
#402 = DIRECTION('',(0.,0.,1.));
#403 = ORIENTED_EDGE('',*,*,#404,.F.);
#404 = EDGE_CURVE('',#58,#397,#405,.T.);
#405 = LINE('',#406,#407);
#406 = CARTESIAN_POINT('',(0.,0.,0.));
#407 = VECTOR('',#408,1.);
#408 = DIRECTION('',(1.,0.,-0.));
#409 = ORIENTED_EDGE('',*,*,#57,.T.);
#410 = ORIENTED_EDGE('',*,*,#323,.T.);
#411 = PLANE('',#412);
#412 = AXIS2_PLACEMENT_3D('',#413,#414,#415);
#413 = CARTESIAN_POINT('',(0.,0.,0.));
#414 = DIRECTION('',(-0.,1.,0.));
#415 = DIRECTION('',(0.,0.,1.));
#416 = ADVANCED_FACE('',(#417,#451,#462),#473,.F.);
#417 = FACE_BOUND('',#418,.F.);
#418 = EDGE_LOOP('',(#419,#420,#428,#436,#444,#450));
#419 = ORIENTED_EDGE('',*,*,#90,.F.);
#420 = ORIENTED_EDGE('',*,*,#421,.T.);
#421 = EDGE_CURVE('',#82,#422,#424,.T.);
#422 = VERTEX_POINT('',#423);
#423 = CARTESIAN_POINT('',(228.,10.,30.));
#424 = LINE('',#425,#426);
#425 = CARTESIAN_POINT('',(0.,10.,30.));
#426 = VECTOR('',#427,1.);
#427 = DIRECTION('',(1.,0.,-0.));
#428 = ORIENTED_EDGE('',*,*,#429,.F.);
#429 = EDGE_CURVE('',#430,#422,#432,.T.);
#430 = VERTEX_POINT('',#431);
#431 = CARTESIAN_POINT('',(230.,10.,30.));
#432 = LINE('',#433,#434);
#433 = CARTESIAN_POINT('',(114.,10.,30.));
#434 = VECTOR('',#435,1.);
#435 = DIRECTION('',(-1.,-0.,0.));
#436 = ORIENTED_EDGE('',*,*,#437,.F.);
#437 = EDGE_CURVE('',#438,#430,#440,.T.);
#438 = VERTEX_POINT('',#439);
#439 = CARTESIAN_POINT('',(230.,60.,30.));
#440 = LINE('',#441,#442);
#441 = CARTESIAN_POINT('',(230.,2.5,30.));
#442 = VECTOR('',#443,1.);
#443 = DIRECTION('',(0.,-1.,0.));
#444 = ORIENTED_EDGE('',*,*,#445,.T.);
#445 = EDGE_CURVE('',#438,#366,#446,.T.);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(114.,60.,30.));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(-1.,-0.,0.));
#450 = ORIENTED_EDGE('',*,*,#365,.F.);
#451 = FACE_BOUND('',#452,.F.);
#452 = EDGE_LOOP('',(#453));
#453 = ORIENTED_EDGE('',*,*,#454,.F.);
#454 = EDGE_CURVE('',#455,#455,#457,.T.);
#455 = VERTEX_POINT('',#456);
#456 = CARTESIAN_POINT('',(51.,35.,30.));
#457 = CIRCLE('',#458,11.);
#458 = AXIS2_PLACEMENT_3D('',#459,#460,#461);
#459 = CARTESIAN_POINT('',(40.,35.,30.));
#460 = DIRECTION('',(0.,0.,1.));
#461 = DIRECTION('',(1.,0.,-0.));
#462 = FACE_BOUND('',#463,.F.);
#463 = EDGE_LOOP('',(#464));
#464 = ORIENTED_EDGE('',*,*,#465,.F.);
#465 = EDGE_CURVE('',#466,#466,#468,.T.);
#466 = VERTEX_POINT('',#467);
#467 = CARTESIAN_POINT('',(161.,35.,30.));
#468 = CIRCLE('',#469,11.);
#469 = AXIS2_PLACEMENT_3D('',#470,#471,#472);
#470 = CARTESIAN_POINT('',(150.,35.,30.));
#471 = DIRECTION('',(0.,0.,1.));
#472 = DIRECTION('',(1.,0.,-0.));
#473 = PLANE('',#474);
#474 = AXIS2_PLACEMENT_3D('',#475,#476,#477);
#475 = CARTESIAN_POINT('',(0.,5.,30.));
#476 = DIRECTION('',(0.,0.,1.));
#477 = DIRECTION('',(1.,0.,-0.));
#478 = ADVANCED_FACE('',(#479),#497,.F.);
#479 = FACE_BOUND('',#480,.F.);
#480 = EDGE_LOOP('',(#481,#482,#483,#491));
#481 = ORIENTED_EDGE('',*,*,#65,.F.);
#482 = ORIENTED_EDGE('',*,*,#404,.T.);
#483 = ORIENTED_EDGE('',*,*,#484,.T.);
#484 = EDGE_CURVE('',#397,#485,#487,.T.);
#485 = VERTEX_POINT('',#486);
#486 = CARTESIAN_POINT('',(230.,5.,0.));
#487 = LINE('',#488,#489);
#488 = CARTESIAN_POINT('',(230.,0.,0.));
#489 = VECTOR('',#490,1.);
#490 = DIRECTION('',(-0.,1.,0.));
#491 = ORIENTED_EDGE('',*,*,#492,.F.);
#492 = EDGE_CURVE('',#66,#485,#493,.T.);
#493 = LINE('',#494,#495);
#494 = CARTESIAN_POINT('',(0.,5.,0.));
#495 = VECTOR('',#496,1.);
#496 = DIRECTION('',(1.,0.,-0.));
#497 = PLANE('',#498);
#498 = AXIS2_PLACEMENT_3D('',#499,#500,#501);
#499 = CARTESIAN_POINT('',(0.,0.,0.));
#500 = DIRECTION('',(0.,0.,1.));
#501 = DIRECTION('',(1.,0.,-0.));
#502 = ADVANCED_FACE('',(#503),#539,.F.);
#503 = FACE_BOUND('',#504,.F.);
#504 = EDGE_LOOP('',(#505,#514,#515,#516,#524,#533));
#505 = ORIENTED_EDGE('',*,*,#506,.F.);
#506 = EDGE_CURVE('',#422,#507,#509,.T.);
#507 = VERTEX_POINT('',#508);
#508 = CARTESIAN_POINT('',(228.,6.,28.));
#509 = CIRCLE('',#510,5.);
#510 = AXIS2_PLACEMENT_3D('',#511,#512,#513);
#511 = CARTESIAN_POINT('',(228.,10.,25.));
#512 = DIRECTION('',(1.,0.,-0.));
#513 = DIRECTION('',(0.,0.,1.));
#514 = ORIENTED_EDGE('',*,*,#421,.F.);
#515 = ORIENTED_EDGE('',*,*,#81,.T.);
#516 = ORIENTED_EDGE('',*,*,#517,.T.);
#517 = EDGE_CURVE('',#74,#518,#520,.T.);
#518 = VERTEX_POINT('',#519);
#519 = CARTESIAN_POINT('',(230.,5.,25.));
#520 = LINE('',#521,#522);
#521 = CARTESIAN_POINT('',(0.,5.,25.));
#522 = VECTOR('',#523,1.);
#523 = DIRECTION('',(1.,0.,-0.));
#524 = ORIENTED_EDGE('',*,*,#525,.F.);
#525 = EDGE_CURVE('',#526,#518,#528,.T.);
#526 = VERTEX_POINT('',#527);
#527 = CARTESIAN_POINT('',(230.,6.,28.));
#528 = CIRCLE('',#529,5.);
#529 = AXIS2_PLACEMENT_3D('',#530,#531,#532);
#530 = CARTESIAN_POINT('',(230.,10.,25.));
#531 = DIRECTION('',(1.,0.,-0.));
#532 = DIRECTION('',(0.,0.,1.));
#533 = ORIENTED_EDGE('',*,*,#534,.F.);
#534 = EDGE_CURVE('',#507,#526,#535,.T.);
#535 = LINE('',#536,#537);
#536 = CARTESIAN_POINT('',(0.,6.,28.));
#537 = VECTOR('',#538,1.);
#538 = DIRECTION('',(1.,0.,-0.));
#539 = CYLINDRICAL_SURFACE('',#540,5.);
#540 = AXIS2_PLACEMENT_3D('',#541,#542,#543);
#541 = CARTESIAN_POINT('',(0.,10.,25.));
#542 = DIRECTION('',(1.,0.,-0.));
#543 = DIRECTION('',(0.,0.,1.));
#544 = ADVANCED_FACE('',(#545),#556,.T.);
#545 = FACE_BOUND('',#546,.T.);
#546 = EDGE_LOOP('',(#547,#553,#554,#555));
#547 = ORIENTED_EDGE('',*,*,#548,.F.);
#548 = EDGE_CURVE('',#485,#518,#549,.T.);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(230.,5.,0.));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,1.));
#553 = ORIENTED_EDGE('',*,*,#492,.F.);
#554 = ORIENTED_EDGE('',*,*,#73,.T.);
#555 = ORIENTED_EDGE('',*,*,#517,.T.);
#556 = PLANE('',#557);
#557 = AXIS2_PLACEMENT_3D('',#558,#559,#560);
#558 = CARTESIAN_POINT('',(0.,5.,0.));
#559 = DIRECTION('',(-0.,1.,0.));
#560 = DIRECTION('',(0.,0.,1.));
#561 = ADVANCED_FACE('',(#562),#575,.T.);
#562 = FACE_BOUND('',#563,.T.);
#563 = EDGE_LOOP('',(#564,#570,#571,#572,#573,#574));
#564 = ORIENTED_EDGE('',*,*,#565,.F.);
#565 = EDGE_CURVE('',#355,#161,#566,.T.);
#566 = LINE('',#567,#568);
#567 = CARTESIAN_POINT('',(230.,60.,28.));
#568 = VECTOR('',#569,1.);
#569 = DIRECTION('',(-0.,1.,0.));
#570 = ORIENTED_EDGE('',*,*,#354,.F.);
#571 = ORIENTED_EDGE('',*,*,#288,.F.);
#572 = ORIENTED_EDGE('',*,*,#211,.T.);
#573 = ORIENTED_EDGE('',*,*,#129,.T.);
#574 = ORIENTED_EDGE('',*,*,#160,.T.);
#575 = PLANE('',#576);
#576 = AXIS2_PLACEMENT_3D('',#577,#578,#579);
#577 = CARTESIAN_POINT('',(230.,0.,0.));
#578 = DIRECTION('',(1.,0.,-0.));
#579 = DIRECTION('',(0.,0.,1.));
#580 = ADVANCED_FACE('',(#581),#592,.T.);
#581 = FACE_BOUND('',#582,.T.);
#582 = EDGE_LOOP('',(#583,#589,#590,#591));
#583 = ORIENTED_EDGE('',*,*,#584,.F.);
#584 = EDGE_CURVE('',#374,#180,#585,.T.);
#585 = LINE('',#586,#587);
#586 = CARTESIAN_POINT('',(228.,60.,28.));
#587 = VECTOR('',#588,1.);
#588 = DIRECTION('',(-0.,1.,0.));
#589 = ORIENTED_EDGE('',*,*,#382,.F.);
#590 = ORIENTED_EDGE('',*,*,#565,.T.);
#591 = ORIENTED_EDGE('',*,*,#188,.T.);
#592 = PLANE('',#593);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(228.,60.,28.));
#595 = DIRECTION('',(0.,0.,1.));
#596 = DIRECTION('',(1.,0.,-0.));
#597 = ADVANCED_FACE('',(#598),#609,.T.);
#598 = FACE_BOUND('',#599,.T.);
#599 = EDGE_LOOP('',(#600,#601,#602,#608));
#600 = ORIENTED_EDGE('',*,*,#584,.T.);
#601 = ORIENTED_EDGE('',*,*,#179,.T.);
#602 = ORIENTED_EDGE('',*,*,#603,.F.);
#603 = EDGE_CURVE('',#366,#172,#604,.T.);
#604 = LINE('',#605,#606);
#605 = CARTESIAN_POINT('',(228.,60.,28.));
#606 = VECTOR('',#607,1.);
#607 = DIRECTION('',(0.,0.,1.));
#608 = ORIENTED_EDGE('',*,*,#373,.F.);
#609 = PLANE('',#610);
#610 = AXIS2_PLACEMENT_3D('',#611,#612,#613);
#611 = CARTESIAN_POINT('',(228.,60.,28.));
#612 = DIRECTION('',(1.,0.,-0.));
#613 = DIRECTION('',(0.,0.,1.));
#614 = ADVANCED_FACE('',(#615),#631,.F.);
#615 = FACE_BOUND('',#616,.F.);
#616 = EDGE_LOOP('',(#617,#623,#624,#630));
#617 = ORIENTED_EDGE('',*,*,#618,.F.);
#618 = EDGE_CURVE('',#430,#236,#619,.T.);
#619 = LINE('',#620,#621);
#620 = CARTESIAN_POINT('',(230.,10.,28.));
#621 = VECTOR('',#622,1.);
#622 = DIRECTION('',(0.,0.,1.));
#623 = ORIENTED_EDGE('',*,*,#429,.T.);
#624 = ORIENTED_EDGE('',*,*,#625,.T.);
#625 = EDGE_CURVE('',#422,#228,#626,.T.);
#626 = LINE('',#627,#628);
#627 = CARTESIAN_POINT('',(228.,10.,28.));
#628 = VECTOR('',#629,1.);
#629 = DIRECTION('',(0.,0.,1.));
#630 = ORIENTED_EDGE('',*,*,#235,.T.);
#631 = PLANE('',#632);
#632 = AXIS2_PLACEMENT_3D('',#633,#634,#635);
#633 = CARTESIAN_POINT('',(228.,10.,28.));
#634 = DIRECTION('',(-0.,1.,0.));
#635 = DIRECTION('',(0.,0.,1.));
#636 = ADVANCED_FACE('',(#637),#648,.T.);
#637 = FACE_BOUND('',#638,.T.);
#638 = EDGE_LOOP('',(#639,#645,#646,#647));
#639 = ORIENTED_EDGE('',*,*,#640,.F.);
#640 = EDGE_CURVE('',#438,#244,#641,.T.);
#641 = LINE('',#642,#643);
#642 = CARTESIAN_POINT('',(230.,60.,28.));
#643 = VECTOR('',#644,1.);
#644 = DIRECTION('',(0.,0.,1.));
#645 = ORIENTED_EDGE('',*,*,#445,.T.);
#646 = ORIENTED_EDGE('',*,*,#603,.T.);
#647 = ORIENTED_EDGE('',*,*,#251,.T.);
#648 = PLANE('',#649);
#649 = AXIS2_PLACEMENT_3D('',#650,#651,#652);
#650 = CARTESIAN_POINT('',(228.,60.,28.));
#651 = DIRECTION('',(-0.,1.,0.));
#652 = DIRECTION('',(0.,0.,1.));
#653 = ADVANCED_FACE('',(#654),#660,.T.);
#654 = FACE_BOUND('',#655,.T.);
#655 = EDGE_LOOP('',(#656,#657,#658,#659));
#656 = ORIENTED_EDGE('',*,*,#243,.F.);
#657 = ORIENTED_EDGE('',*,*,#618,.F.);
#658 = ORIENTED_EDGE('',*,*,#437,.F.);
#659 = ORIENTED_EDGE('',*,*,#640,.T.);
#660 = PLANE('',#661);
#661 = AXIS2_PLACEMENT_3D('',#662,#663,#664);
#662 = CARTESIAN_POINT('',(230.,0.,0.));
#663 = DIRECTION('',(1.,0.,-0.));
#664 = DIRECTION('',(0.,0.,1.));
#665 = ADVANCED_FACE('',(#666),#677,.F.);
#666 = FACE_BOUND('',#667,.F.);
#667 = EDGE_LOOP('',(#668,#669,#675,#676));
#668 = ORIENTED_EDGE('',*,*,#260,.F.);
#669 = ORIENTED_EDGE('',*,*,#670,.F.);
#670 = EDGE_CURVE('',#455,#261,#671,.T.);
#671 = LINE('',#672,#673);
#672 = CARTESIAN_POINT('',(51.,35.,29.));
#673 = VECTOR('',#674,1.);
#674 = DIRECTION('',(0.,0.,1.));
#675 = ORIENTED_EDGE('',*,*,#454,.T.);
#676 = ORIENTED_EDGE('',*,*,#670,.T.);
#677 = CYLINDRICAL_SURFACE('',#678,11.);
#678 = AXIS2_PLACEMENT_3D('',#679,#680,#681);
#679 = CARTESIAN_POINT('',(40.,35.,29.));
#680 = DIRECTION('',(0.,0.,1.));
#681 = DIRECTION('',(1.,0.,-0.));
#682 = ADVANCED_FACE('',(#683),#694,.F.);
#683 = FACE_BOUND('',#684,.F.);
#684 = EDGE_LOOP('',(#685,#686,#692,#693));
#685 = ORIENTED_EDGE('',*,*,#271,.F.);
#686 = ORIENTED_EDGE('',*,*,#687,.F.);
#687 = EDGE_CURVE('',#466,#272,#688,.T.);
#688 = LINE('',#689,#690);
#689 = CARTESIAN_POINT('',(161.,35.,29.));
#690 = VECTOR('',#691,1.);
#691 = DIRECTION('',(0.,0.,1.));
#692 = ORIENTED_EDGE('',*,*,#465,.T.);
#693 = ORIENTED_EDGE('',*,*,#687,.T.);
#694 = CYLINDRICAL_SURFACE('',#695,11.);
#695 = AXIS2_PLACEMENT_3D('',#696,#697,#698);
#696 = CARTESIAN_POINT('',(150.,35.,29.));
#697 = DIRECTION('',(0.,0.,1.));
#698 = DIRECTION('',(1.,0.,-0.));
#699 = ADVANCED_FACE('',(#700),#711,.T.);
#700 = FACE_BOUND('',#701,.T.);
#701 = EDGE_LOOP('',(#702,#708,#709,#710));
#702 = ORIENTED_EDGE('',*,*,#703,.T.);
#703 = EDGE_CURVE('',#313,#507,#704,.T.);
#704 = LINE('',#705,#706);
#705 = CARTESIAN_POINT('',(228.,0.,28.));
#706 = VECTOR('',#707,1.);
#707 = DIRECTION('',(-0.,1.,0.));
#708 = ORIENTED_EDGE('',*,*,#506,.F.);
#709 = ORIENTED_EDGE('',*,*,#625,.T.);
#710 = ORIENTED_EDGE('',*,*,#312,.T.);
#711 = PLANE('',#712);
#712 = AXIS2_PLACEMENT_3D('',#713,#714,#715);
#713 = CARTESIAN_POINT('',(228.,0.,28.));
#714 = DIRECTION('',(1.,0.,-0.));
#715 = DIRECTION('',(0.,0.,1.));
#716 = ADVANCED_FACE('',(#717),#728,.T.);
#717 = FACE_BOUND('',#718,.T.);
#718 = EDGE_LOOP('',(#719,#720,#721,#727));
#719 = ORIENTED_EDGE('',*,*,#703,.F.);
#720 = ORIENTED_EDGE('',*,*,#340,.T.);
#721 = ORIENTED_EDGE('',*,*,#722,.T.);
#722 = EDGE_CURVE('',#332,#526,#723,.T.);
#723 = LINE('',#724,#725);
#724 = CARTESIAN_POINT('',(230.,0.,28.));
#725 = VECTOR('',#726,1.);
#726 = DIRECTION('',(-0.,1.,0.));
#727 = ORIENTED_EDGE('',*,*,#534,.F.);
#728 = PLANE('',#729);
#729 = AXIS2_PLACEMENT_3D('',#730,#731,#732);
#730 = CARTESIAN_POINT('',(228.,0.,28.));
#731 = DIRECTION('',(0.,0.,1.));
#732 = DIRECTION('',(1.,0.,-0.));
#733 = ADVANCED_FACE('',(#734),#742,.T.);
#734 = FACE_BOUND('',#735,.T.);
#735 = EDGE_LOOP('',(#736,#737,#738,#739,#740,#741));
#736 = ORIENTED_EDGE('',*,*,#722,.F.);
#737 = ORIENTED_EDGE('',*,*,#331,.T.);
#738 = ORIENTED_EDGE('',*,*,#396,.F.);
#739 = ORIENTED_EDGE('',*,*,#484,.T.);
#740 = ORIENTED_EDGE('',*,*,#548,.T.);
#741 = ORIENTED_EDGE('',*,*,#525,.F.);
#742 = PLANE('',#743);
#743 = AXIS2_PLACEMENT_3D('',#744,#745,#746);
#744 = CARTESIAN_POINT('',(230.,0.,0.));
#745 = DIRECTION('',(1.,0.,-0.));
#746 = DIRECTION('',(0.,0.,1.));
#747 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#751)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#748,#749,#750)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#748 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#749 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#750 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#751 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#748,
  'distance_accuracy_value','confusion accuracy');
#752 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#753 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#754)
  ,#747);
#754 = STYLED_ITEM('color',(#755),#15);
#755 = PRESENTATION_STYLE_ASSIGNMENT((#756,#762));
#756 = SURFACE_STYLE_USAGE(.BOTH.,#757);
#757 = SURFACE_SIDE_STYLE('',(#758));
#758 = SURFACE_STYLE_FILL_AREA(#759);
#759 = FILL_AREA_STYLE('',(#760));
#760 = FILL_AREA_STYLE_COLOUR('',#761);
#761 = COLOUR_RGB('',0.447059003357,0.474510015008,0.501960993452);
#762 = CURVE_STYLE('',#763,POSITIVE_LENGTH_MEASURE(0.1),#764);
#763 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#764 = COLOUR_RGB('',9.803921802644E-02,9.803921802644E-02,
  9.803921802644E-02);
ENDSEC;
END-ISO-10303-21;
