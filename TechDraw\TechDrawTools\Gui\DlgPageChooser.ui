<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TechDrawGui::DlgPageChooser</class>
 <widget class="QDialog" name="TechDrawGui::DlgPageChooser">
  <property name="windowModality">
   <enum>Qt::WindowModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>360</width>
    <height>280</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Page Chooser</string>
  </property>
  <property name="toolTip">
   <string/>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="lPrompt">
     <property name="text">
      <string>FreeCAD could not determine which Page to use.  Please select a Page.</string>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QListWidget" name="lwPages">
     <property name="toolTip">
      <string>Select a Page that should be used</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="bbButtons">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
     <property name="centerButtons">
      <bool>false</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>bbButtons</sender>
   <signal>accepted()</signal>
   <receiver>TechDrawGui::DlgPageChooser</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>179</x>
     <y>228</y>
    </hint>
    <hint type="destinationlabel">
     <x>179</x>
     <y>139</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>bbButtons</sender>
   <signal>rejected()</signal>
   <receiver>TechDrawGui::DlgPageChooser</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>179</x>
     <y>228</y>
    </hint>
    <hint type="destinationlabel">
     <x>179</x>
     <y>139</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
