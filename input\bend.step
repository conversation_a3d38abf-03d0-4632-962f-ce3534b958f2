ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-06-30T10:49:02',('Author'),(
    ''),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Sheet_Steel_L_bend','Sheet_Steel_L_bend','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#231);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#88,#119,#150,#181,#203,#217));
#17 = ADVANCED_FACE('',(#18),#52,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.T.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,0.,0.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,0.,40.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,0.,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#24,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(60.,0.,40.));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(0.,0.,40.));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(1.,0.,0.));
#38 = ORIENTED_EDGE('',*,*,#39,.F.);
#39 = EDGE_CURVE('',#40,#32,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(60.,0.,0.));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(60.,0.,0.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.,0.,1.));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#22,#40,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(0.,0.,0.));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(1.,0.,0.));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(0.,0.,0.));
#55 = DIRECTION('',(0.,1.,0.));
#56 = DIRECTION('',(1.,0.,0.));
#57 = ADVANCED_FACE('',(#58),#83,.F.);
#58 = FACE_BOUND('',#59,.F.);
#59 = EDGE_LOOP('',(#60,#61,#69,#77));
#60 = ORIENTED_EDGE('',*,*,#39,.T.);
#61 = ORIENTED_EDGE('',*,*,#62,.T.);
#62 = EDGE_CURVE('',#32,#63,#65,.T.);
#63 = VERTEX_POINT('',#64);
#64 = CARTESIAN_POINT('',(60.,1.,40.));
#65 = LINE('',#66,#67);
#66 = CARTESIAN_POINT('',(60.,0.,40.));
#67 = VECTOR('',#68,1.);
#68 = DIRECTION('',(0.,1.,0.));
#69 = ORIENTED_EDGE('',*,*,#70,.F.);
#70 = EDGE_CURVE('',#71,#63,#73,.T.);
#71 = VERTEX_POINT('',#72);
#72 = CARTESIAN_POINT('',(60.,1.,0.));
#73 = LINE('',#74,#75);
#74 = CARTESIAN_POINT('',(60.,1.,0.));
#75 = VECTOR('',#76,1.);
#76 = DIRECTION('',(0.,0.,1.));
#77 = ORIENTED_EDGE('',*,*,#78,.F.);
#78 = EDGE_CURVE('',#40,#71,#79,.T.);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(60.,0.,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(0.,1.,0.));
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(60.,0.,0.));
#86 = DIRECTION('',(-1.,0.,0.));
#87 = DIRECTION('',(0.,1.,0.));
#88 = ADVANCED_FACE('',(#89),#114,.F.);
#89 = FACE_BOUND('',#90,.F.);
#90 = EDGE_LOOP('',(#91,#92,#100,#108));
#91 = ORIENTED_EDGE('',*,*,#70,.T.);
#92 = ORIENTED_EDGE('',*,*,#93,.T.);
#93 = EDGE_CURVE('',#63,#94,#96,.T.);
#94 = VERTEX_POINT('',#95);
#95 = CARTESIAN_POINT('',(0.267949192431,1.,40.));
#96 = LINE('',#97,#98);
#97 = CARTESIAN_POINT('',(60.,1.,40.));
#98 = VECTOR('',#99,1.);
#99 = DIRECTION('',(-1.,0.,0.));
#100 = ORIENTED_EDGE('',*,*,#101,.F.);
#101 = EDGE_CURVE('',#102,#94,#104,.T.);
#102 = VERTEX_POINT('',#103);
#103 = CARTESIAN_POINT('',(0.267949192431,1.,0.));
#104 = LINE('',#105,#106);
#105 = CARTESIAN_POINT('',(0.267949192431,1.,0.));
#106 = VECTOR('',#107,1.);
#107 = DIRECTION('',(0.,0.,1.));
#108 = ORIENTED_EDGE('',*,*,#109,.F.);
#109 = EDGE_CURVE('',#71,#102,#110,.T.);
#110 = LINE('',#111,#112);
#111 = CARTESIAN_POINT('',(60.,1.,0.));
#112 = VECTOR('',#113,1.);
#113 = DIRECTION('',(-1.,0.,0.));
#114 = PLANE('',#115);
#115 = AXIS2_PLACEMENT_3D('',#116,#117,#118);
#116 = CARTESIAN_POINT('',(60.,1.,0.));
#117 = DIRECTION('',(0.,-1.,0.));
#118 = DIRECTION('',(-1.,0.,0.));
#119 = ADVANCED_FACE('',(#120),#145,.F.);
#120 = FACE_BOUND('',#121,.F.);
#121 = EDGE_LOOP('',(#122,#123,#131,#139));
#122 = ORIENTED_EDGE('',*,*,#101,.T.);
#123 = ORIENTED_EDGE('',*,*,#124,.T.);
#124 = EDGE_CURVE('',#94,#125,#127,.T.);
#125 = VERTEX_POINT('',#126);
#126 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,40.));
#127 = LINE('',#128,#129);
#128 = CARTESIAN_POINT('',(0.267949192431,1.,40.));
#129 = VECTOR('',#130,1.);
#130 = DIRECTION('',(0.5,0.866025403784,0.));
#131 = ORIENTED_EDGE('',*,*,#132,.F.);
#132 = EDGE_CURVE('',#133,#125,#135,.T.);
#133 = VERTEX_POINT('',#134);
#134 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,0.));
#135 = LINE('',#136,#137);
#136 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,0.));
#137 = VECTOR('',#138,1.);
#138 = DIRECTION('',(0.,0.,1.));
#139 = ORIENTED_EDGE('',*,*,#140,.F.);
#140 = EDGE_CURVE('',#102,#133,#141,.T.);
#141 = LINE('',#142,#143);
#142 = CARTESIAN_POINT('',(0.267949192431,1.,0.));
#143 = VECTOR('',#144,1.);
#144 = DIRECTION('',(0.5,0.866025403784,0.));
#145 = PLANE('',#146);
#146 = AXIS2_PLACEMENT_3D('',#147,#148,#149);
#147 = CARTESIAN_POINT('',(0.267949192431,1.,0.));
#148 = DIRECTION('',(-0.866025403784,0.5,0.));
#149 = DIRECTION('',(0.5,0.866025403784,0.));
#150 = ADVANCED_FACE('',(#151),#176,.F.);
#151 = FACE_BOUND('',#152,.F.);
#152 = EDGE_LOOP('',(#153,#154,#162,#170));
#153 = ORIENTED_EDGE('',*,*,#132,.T.);
#154 = ORIENTED_EDGE('',*,*,#155,.T.);
#155 = EDGE_CURVE('',#125,#156,#158,.T.);
#156 = VERTEX_POINT('',#157);
#157 = CARTESIAN_POINT('',(20.,34.641016151378,40.));
#158 = LINE('',#159,#160);
#159 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,40.));
#160 = VECTOR('',#161,1.);
#161 = DIRECTION('',(-0.171861884667,-0.985121054794,0.));
#162 = ORIENTED_EDGE('',*,*,#163,.F.);
#163 = EDGE_CURVE('',#164,#156,#166,.T.);
#164 = VERTEX_POINT('',#165);
#165 = CARTESIAN_POINT('',(20.,34.641016151378,0.));
#166 = LINE('',#167,#168);
#167 = CARTESIAN_POINT('',(20.,34.641016151378,0.));
#168 = VECTOR('',#169,1.);
#169 = DIRECTION('',(0.,0.,1.));
#170 = ORIENTED_EDGE('',*,*,#171,.F.);
#171 = EDGE_CURVE('',#133,#164,#172,.T.);
#172 = LINE('',#173,#174);
#173 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,0.));
#174 = VECTOR('',#175,1.);
#175 = DIRECTION('',(-0.171861884667,-0.985121054794,0.));
#176 = PLANE('',#177);
#177 = AXIS2_PLACEMENT_3D('',#178,#179,#180);
#178 = CARTESIAN_POINT('',(20.133974596216,35.408965343809,0.));
#179 = DIRECTION('',(0.985121054794,-0.171861884667,0.));
#180 = DIRECTION('',(-0.171861884667,-0.985121054794,0.));
#181 = ADVANCED_FACE('',(#182),#198,.F.);
#182 = FACE_BOUND('',#183,.F.);
#183 = EDGE_LOOP('',(#184,#185,#191,#192));
#184 = ORIENTED_EDGE('',*,*,#163,.T.);
#185 = ORIENTED_EDGE('',*,*,#186,.T.);
#186 = EDGE_CURVE('',#156,#24,#187,.T.);
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(20.,34.641016151378,40.));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(-0.5,-0.866025403784,0.));
#191 = ORIENTED_EDGE('',*,*,#21,.F.);
#192 = ORIENTED_EDGE('',*,*,#193,.F.);
#193 = EDGE_CURVE('',#164,#22,#194,.T.);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(20.,34.641016151378,0.));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(-0.5,-0.866025403784,0.));
#198 = PLANE('',#199);
#199 = AXIS2_PLACEMENT_3D('',#200,#201,#202);
#200 = CARTESIAN_POINT('',(20.,34.641016151378,0.));
#201 = DIRECTION('',(0.866025403784,-0.5,0.));
#202 = DIRECTION('',(-0.5,-0.866025403784,0.));
#203 = ADVANCED_FACE('',(#204),#212,.F.);
#204 = FACE_BOUND('',#205,.F.);
#205 = EDGE_LOOP('',(#206,#207,#208,#209,#210,#211));
#206 = ORIENTED_EDGE('',*,*,#47,.T.);
#207 = ORIENTED_EDGE('',*,*,#78,.T.);
#208 = ORIENTED_EDGE('',*,*,#109,.T.);
#209 = ORIENTED_EDGE('',*,*,#140,.T.);
#210 = ORIENTED_EDGE('',*,*,#171,.T.);
#211 = ORIENTED_EDGE('',*,*,#193,.T.);
#212 = PLANE('',#213);
#213 = AXIS2_PLACEMENT_3D('',#214,#215,#216);
#214 = CARTESIAN_POINT('',(22.266105917732,7.47182566946,0.));
#215 = DIRECTION('',(0.,0.,1.));
#216 = DIRECTION('',(1.,0.,-0.));
#217 = ADVANCED_FACE('',(#218),#226,.T.);
#218 = FACE_BOUND('',#219,.T.);
#219 = EDGE_LOOP('',(#220,#221,#222,#223,#224,#225));
#220 = ORIENTED_EDGE('',*,*,#31,.T.);
#221 = ORIENTED_EDGE('',*,*,#62,.T.);
#222 = ORIENTED_EDGE('',*,*,#93,.T.);
#223 = ORIENTED_EDGE('',*,*,#124,.T.);
#224 = ORIENTED_EDGE('',*,*,#155,.T.);
#225 = ORIENTED_EDGE('',*,*,#186,.T.);
#226 = PLANE('',#227);
#227 = AXIS2_PLACEMENT_3D('',#228,#229,#230);
#228 = CARTESIAN_POINT('',(22.266105917732,7.47182566946,40.));
#229 = DIRECTION('',(0.,0.,1.));
#230 = DIRECTION('',(1.,0.,-0.));
#231 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#235)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#232,#233,#234)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#232 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#233 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#234 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#235 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#232,
  'distance_accuracy_value','confusion accuracy');
#236 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
