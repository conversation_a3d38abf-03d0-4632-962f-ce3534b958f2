ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-08-20T09:00:15',('Author'),(
    ''),'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Square_tube_40x40x800_t2_Rint2_Rext4_bevel45_4xD8',
  'Square_tube_40x40x800_t2_Rint2_Rext4_bevel45_4xD8','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#597);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#68,#126,#202,#253,#322,#347,#398,#409,#420,
    #445,#456,#489,#509,#529,#552,#563,#580));
#17 = ADVANCED_FACE('',(#18,#52),#63,.T.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(40.,40.,40.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(760.,40.,40.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,40.,40.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(1.,0.,-0.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(40.,0.,40.));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(40.,40.,40.));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(0.,-1.,0.));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(760.,0.,40.));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(0.,0.,40.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(1.,0.,-0.));
#46 = ORIENTED_EDGE('',*,*,#47,.T.);
#47 = EDGE_CURVE('',#40,#24,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(760.,0.,40.));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(0.,1.,0.));
#52 = FACE_BOUND('',#53,.T.);
#53 = EDGE_LOOP('',(#54));
#54 = ORIENTED_EDGE('',*,*,#55,.T.);
#55 = EDGE_CURVE('',#56,#56,#58,.T.);
#56 = VERTEX_POINT('',#57);
#57 = CARTESIAN_POINT('',(396.,20.,40.));
#58 = CIRCLE('',#59,4.);
#59 = AXIS2_PLACEMENT_3D('',#60,#61,#62);
#60 = CARTESIAN_POINT('',(400.,20.,40.));
#61 = DIRECTION('',(0.,0.,-1.));
#62 = DIRECTION('',(-1.,0.,-0.));
#63 = PLANE('',#64);
#64 = AXIS2_PLACEMENT_3D('',#65,#66,#67);
#65 = CARTESIAN_POINT('',(0.,0.,40.));
#66 = DIRECTION('',(0.,0.,1.));
#67 = DIRECTION('',(1.,0.,-0.));
#68 = ADVANCED_FACE('',(#69,#110),#121,.T.);
#69 = FACE_BOUND('',#70,.T.);
#70 = EDGE_LOOP('',(#71,#81,#89,#95,#96,#104));
#71 = ORIENTED_EDGE('',*,*,#72,.F.);
#72 = EDGE_CURVE('',#73,#75,#77,.T.);
#73 = VERTEX_POINT('',#74);
#74 = CARTESIAN_POINT('',(4.,40.,0.));
#75 = VERTEX_POINT('',#76);
#76 = CARTESIAN_POINT('',(796.,40.,0.));
#77 = LINE('',#78,#79);
#78 = CARTESIAN_POINT('',(0.,40.,0.));
#79 = VECTOR('',#80,1.);
#80 = DIRECTION('',(1.,0.,-0.));
#81 = ORIENTED_EDGE('',*,*,#82,.T.);
#82 = EDGE_CURVE('',#73,#83,#85,.T.);
#83 = VERTEX_POINT('',#84);
#84 = CARTESIAN_POINT('',(4.,40.,4.));
#85 = LINE('',#86,#87);
#86 = CARTESIAN_POINT('',(4.,40.,0.));
#87 = VECTOR('',#88,1.);
#88 = DIRECTION('',(0.,0.,1.));
#89 = ORIENTED_EDGE('',*,*,#90,.T.);
#90 = EDGE_CURVE('',#83,#22,#91,.T.);
#91 = LINE('',#92,#93);
#92 = CARTESIAN_POINT('',(0.,40.,0.));
#93 = VECTOR('',#94,1.);
#94 = DIRECTION('',(0.707106781187,0.,0.707106781187));
#95 = ORIENTED_EDGE('',*,*,#21,.T.);
#96 = ORIENTED_EDGE('',*,*,#97,.T.);
#97 = EDGE_CURVE('',#24,#98,#100,.T.);
#98 = VERTEX_POINT('',#99);
#99 = CARTESIAN_POINT('',(796.,40.,4.));
#100 = LINE('',#101,#102);
#101 = CARTESIAN_POINT('',(760.,40.,40.));
#102 = VECTOR('',#103,1.);
#103 = DIRECTION('',(0.707106781187,0.,-0.707106781187));
#104 = ORIENTED_EDGE('',*,*,#105,.F.);
#105 = EDGE_CURVE('',#75,#98,#106,.T.);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(796.,40.,0.));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,0.,1.));
#110 = FACE_BOUND('',#111,.T.);
#111 = EDGE_LOOP('',(#112));
#112 = ORIENTED_EDGE('',*,*,#113,.T.);
#113 = EDGE_CURVE('',#114,#114,#116,.T.);
#114 = VERTEX_POINT('',#115);
#115 = CARTESIAN_POINT('',(400.,40.,16.));
#116 = CIRCLE('',#117,4.);
#117 = AXIS2_PLACEMENT_3D('',#118,#119,#120);
#118 = CARTESIAN_POINT('',(400.,40.,20.));
#119 = DIRECTION('',(0.,-1.,0.));
#120 = DIRECTION('',(0.,-0.,-1.));
#121 = PLANE('',#122);
#122 = AXIS2_PLACEMENT_3D('',#123,#124,#125);
#123 = CARTESIAN_POINT('',(0.,40.,0.));
#124 = DIRECTION('',(-0.,1.,0.));
#125 = DIRECTION('',(0.,0.,1.));
#126 = ADVANCED_FACE('',(#127,#163),#197,.F.);
#127 = FACE_BOUND('',#128,.F.);
#128 = EDGE_LOOP('',(#129,#137,#138,#139,#148,#156));
#129 = ORIENTED_EDGE('',*,*,#130,.T.);
#130 = EDGE_CURVE('',#131,#40,#133,.T.);
#131 = VERTEX_POINT('',#132);
#132 = CARTESIAN_POINT('',(796.,-4.440892098501E-16,4.));
#133 = LINE('',#134,#135);
#134 = CARTESIAN_POINT('',(800.,0.,0.));
#135 = VECTOR('',#136,1.);
#136 = DIRECTION('',(-0.707106781187,0.,0.707106781187));
#137 = ORIENTED_EDGE('',*,*,#47,.T.);
#138 = ORIENTED_EDGE('',*,*,#97,.T.);
#139 = ORIENTED_EDGE('',*,*,#140,.T.);
#140 = EDGE_CURVE('',#98,#141,#143,.T.);
#141 = VERTEX_POINT('',#142);
#142 = CARTESIAN_POINT('',(800.,36.,0.));
#143 = ELLIPSE('',#144,5.656854249492,4.);
#144 = AXIS2_PLACEMENT_3D('',#145,#146,#147);
#145 = CARTESIAN_POINT('',(796.,36.,4.));
#146 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#147 = DIRECTION('',(-0.707106781187,0.,0.707106781187));
#148 = ORIENTED_EDGE('',*,*,#149,.F.);
#149 = EDGE_CURVE('',#150,#141,#152,.T.);
#150 = VERTEX_POINT('',#151);
#151 = CARTESIAN_POINT('',(800.,4.,0.));
#152 = LINE('',#153,#154);
#153 = CARTESIAN_POINT('',(800.,0.,0.));
#154 = VECTOR('',#155,1.);
#155 = DIRECTION('',(-0.,1.,0.));
#156 = ORIENTED_EDGE('',*,*,#157,.T.);
#157 = EDGE_CURVE('',#150,#131,#158,.T.);
#158 = ELLIPSE('',#159,5.656854249492,4.);
#159 = AXIS2_PLACEMENT_3D('',#160,#161,#162);
#160 = CARTESIAN_POINT('',(796.,4.,4.));
#161 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#162 = DIRECTION('',(-0.707106781187,0.,0.707106781187));
#163 = FACE_BOUND('',#164,.F.);
#164 = EDGE_LOOP('',(#165,#175,#183,#191));
#165 = ORIENTED_EDGE('',*,*,#166,.F.);
#166 = EDGE_CURVE('',#167,#169,#171,.T.);
#167 = VERTEX_POINT('',#168);
#168 = CARTESIAN_POINT('',(798.,2.,2.));
#169 = VERTEX_POINT('',#170);
#170 = CARTESIAN_POINT('',(762.,2.,38.));
#171 = LINE('',#172,#173);
#172 = CARTESIAN_POINT('',(589.5,2.,210.5));
#173 = VECTOR('',#174,1.);
#174 = DIRECTION('',(-0.707106781187,-0.,0.707106781187));
#175 = ORIENTED_EDGE('',*,*,#176,.F.);
#176 = EDGE_CURVE('',#177,#167,#179,.T.);
#177 = VERTEX_POINT('',#178);
#178 = CARTESIAN_POINT('',(798.,38.,2.));
#179 = LINE('',#180,#181);
#180 = CARTESIAN_POINT('',(798.,11.,2.));
#181 = VECTOR('',#182,1.);
#182 = DIRECTION('',(0.,-1.,0.));
#183 = ORIENTED_EDGE('',*,*,#184,.T.);
#184 = EDGE_CURVE('',#177,#185,#187,.T.);
#185 = VERTEX_POINT('',#186);
#186 = CARTESIAN_POINT('',(762.,38.,38.));
#187 = LINE('',#188,#189);
#188 = CARTESIAN_POINT('',(589.5,38.,210.5));
#189 = VECTOR('',#190,1.);
#190 = DIRECTION('',(-0.707106781187,-0.,0.707106781187));
#191 = ORIENTED_EDGE('',*,*,#192,.T.);
#192 = EDGE_CURVE('',#185,#169,#193,.T.);
#193 = LINE('',#194,#195);
#194 = CARTESIAN_POINT('',(762.,11.,38.));
#195 = VECTOR('',#196,1.);
#196 = DIRECTION('',(0.,-1.,0.));
#197 = PLANE('',#198);
#198 = AXIS2_PLACEMENT_3D('',#199,#200,#201);
#199 = CARTESIAN_POINT('',(780.,20.,20.));
#200 = DIRECTION('',(-0.707106781187,-0.,-0.707106781187));
#201 = DIRECTION('',(-0.707106781187,0.,0.707106781187));
#202 = ADVANCED_FACE('',(#203,#237),#248,.F.);
#203 = FACE_BOUND('',#204,.F.);
#204 = EDGE_LOOP('',(#205,#215,#223,#229,#230,#231));
#205 = ORIENTED_EDGE('',*,*,#206,.F.);
#206 = EDGE_CURVE('',#207,#209,#211,.T.);
#207 = VERTEX_POINT('',#208);
#208 = CARTESIAN_POINT('',(4.,-8.881784197001E-16,0.));
#209 = VERTEX_POINT('',#210);
#210 = CARTESIAN_POINT('',(796.,-8.881784197001E-16,0.));
#211 = LINE('',#212,#213);
#212 = CARTESIAN_POINT('',(0.,0.,0.));
#213 = VECTOR('',#214,1.);
#214 = DIRECTION('',(1.,0.,-0.));
#215 = ORIENTED_EDGE('',*,*,#216,.T.);
#216 = EDGE_CURVE('',#207,#217,#219,.T.);
#217 = VERTEX_POINT('',#218);
#218 = CARTESIAN_POINT('',(4.,-4.440892098501E-16,4.));
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(4.,-8.881784197001E-16,0.));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(0.,0.,1.));
#223 = ORIENTED_EDGE('',*,*,#224,.F.);
#224 = EDGE_CURVE('',#32,#217,#225,.T.);
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(40.,0.,40.));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#229 = ORIENTED_EDGE('',*,*,#39,.T.);
#230 = ORIENTED_EDGE('',*,*,#130,.F.);
#231 = ORIENTED_EDGE('',*,*,#232,.F.);
#232 = EDGE_CURVE('',#209,#131,#233,.T.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(796.,-8.881784197001E-16,0.));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.,0.,1.));
#237 = FACE_BOUND('',#238,.F.);
#238 = EDGE_LOOP('',(#239));
#239 = ORIENTED_EDGE('',*,*,#240,.F.);
#240 = EDGE_CURVE('',#241,#241,#243,.T.);
#241 = VERTEX_POINT('',#242);
#242 = CARTESIAN_POINT('',(400.,0.,24.));
#243 = CIRCLE('',#244,4.);
#244 = AXIS2_PLACEMENT_3D('',#245,#246,#247);
#245 = CARTESIAN_POINT('',(400.,0.,20.));
#246 = DIRECTION('',(0.,1.,0.));
#247 = DIRECTION('',(0.,-0.,1.));
#248 = PLANE('',#249);
#249 = AXIS2_PLACEMENT_3D('',#250,#251,#252);
#250 = CARTESIAN_POINT('',(0.,0.,0.));
#251 = DIRECTION('',(-0.,1.,0.));
#252 = DIRECTION('',(0.,0.,1.));
#253 = ADVANCED_FACE('',(#254,#283),#317,.F.);
#254 = FACE_BOUND('',#255,.F.);
#255 = EDGE_LOOP('',(#256,#265,#273,#280,#281,#282));
#256 = ORIENTED_EDGE('',*,*,#257,.T.);
#257 = EDGE_CURVE('',#217,#258,#260,.T.);
#258 = VERTEX_POINT('',#259);
#259 = CARTESIAN_POINT('',(-8.881784197001E-16,4.,0.));
#260 = ELLIPSE('',#261,5.656854249492,4.);
#261 = AXIS2_PLACEMENT_3D('',#262,#263,#264);
#262 = CARTESIAN_POINT('',(4.,4.,4.));
#263 = DIRECTION('',(0.707106781187,0.,-0.707106781187));
#264 = DIRECTION('',(0.707106781187,0.,0.707106781187));
#265 = ORIENTED_EDGE('',*,*,#266,.T.);
#266 = EDGE_CURVE('',#258,#267,#269,.T.);
#267 = VERTEX_POINT('',#268);
#268 = CARTESIAN_POINT('',(-8.881784197001E-16,36.,0.));
#269 = LINE('',#270,#271);
#270 = CARTESIAN_POINT('',(0.,0.,0.));
#271 = VECTOR('',#272,1.);
#272 = DIRECTION('',(-0.,1.,0.));
#273 = ORIENTED_EDGE('',*,*,#274,.T.);
#274 = EDGE_CURVE('',#267,#83,#275,.T.);
#275 = ELLIPSE('',#276,5.656854249492,4.);
#276 = AXIS2_PLACEMENT_3D('',#277,#278,#279);
#277 = CARTESIAN_POINT('',(4.,36.,4.));
#278 = DIRECTION('',(0.707106781187,0.,-0.707106781187));
#279 = DIRECTION('',(0.707106781187,0.,0.707106781187));
#280 = ORIENTED_EDGE('',*,*,#90,.T.);
#281 = ORIENTED_EDGE('',*,*,#31,.T.);
#282 = ORIENTED_EDGE('',*,*,#224,.T.);
#283 = FACE_BOUND('',#284,.F.);
#284 = EDGE_LOOP('',(#285,#295,#303,#311));
#285 = ORIENTED_EDGE('',*,*,#286,.F.);
#286 = EDGE_CURVE('',#287,#289,#291,.T.);
#287 = VERTEX_POINT('',#288);
#288 = CARTESIAN_POINT('',(38.,2.,38.));
#289 = VERTEX_POINT('',#290);
#290 = CARTESIAN_POINT('',(2.,2.,2.));
#291 = LINE('',#292,#293);
#292 = CARTESIAN_POINT('',(10.5,2.,10.5));
#293 = VECTOR('',#294,1.);
#294 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#295 = ORIENTED_EDGE('',*,*,#296,.T.);
#296 = EDGE_CURVE('',#287,#297,#299,.T.);
#297 = VERTEX_POINT('',#298);
#298 = CARTESIAN_POINT('',(38.,38.,38.));
#299 = LINE('',#300,#301);
#300 = CARTESIAN_POINT('',(38.,11.,38.));
#301 = VECTOR('',#302,1.);
#302 = DIRECTION('',(0.,1.,-0.));
#303 = ORIENTED_EDGE('',*,*,#304,.T.);
#304 = EDGE_CURVE('',#297,#305,#307,.T.);
#305 = VERTEX_POINT('',#306);
#306 = CARTESIAN_POINT('',(2.,38.,2.));
#307 = LINE('',#308,#309);
#308 = CARTESIAN_POINT('',(10.5,38.,10.5));
#309 = VECTOR('',#310,1.);
#310 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#311 = ORIENTED_EDGE('',*,*,#312,.F.);
#312 = EDGE_CURVE('',#289,#305,#313,.T.);
#313 = LINE('',#314,#315);
#314 = CARTESIAN_POINT('',(2.,11.,2.));
#315 = VECTOR('',#316,1.);
#316 = DIRECTION('',(0.,1.,-0.));
#317 = PLANE('',#318);
#318 = AXIS2_PLACEMENT_3D('',#319,#320,#321);
#319 = CARTESIAN_POINT('',(20.,20.,20.));
#320 = DIRECTION('',(0.707106781187,-0.,-0.707106781187));
#321 = DIRECTION('',(-0.707106781187,0.,-0.707106781187));
#322 = ADVANCED_FACE('',(#323),#342,.F.);
#323 = FACE_BOUND('',#324,.F.);
#324 = EDGE_LOOP('',(#325,#334,#340,#341));
#325 = ORIENTED_EDGE('',*,*,#326,.F.);
#326 = EDGE_CURVE('',#327,#327,#329,.T.);
#327 = VERTEX_POINT('',#328);
#328 = CARTESIAN_POINT('',(396.,20.,38.));
#329 = CIRCLE('',#330,4.);
#330 = AXIS2_PLACEMENT_3D('',#331,#332,#333);
#331 = CARTESIAN_POINT('',(400.,20.,38.));
#332 = DIRECTION('',(0.,0.,-1.));
#333 = DIRECTION('',(-1.,0.,-0.));
#334 = ORIENTED_EDGE('',*,*,#335,.F.);
#335 = EDGE_CURVE('',#56,#327,#336,.T.);
#336 = LINE('',#337,#338);
#337 = CARTESIAN_POINT('',(396.,20.,41.));
#338 = VECTOR('',#339,1.);
#339 = DIRECTION('',(0.,0.,-1.));
#340 = ORIENTED_EDGE('',*,*,#55,.T.);
#341 = ORIENTED_EDGE('',*,*,#335,.T.);
#342 = CYLINDRICAL_SURFACE('',#343,4.);
#343 = AXIS2_PLACEMENT_3D('',#344,#345,#346);
#344 = CARTESIAN_POINT('',(400.,20.,41.));
#345 = DIRECTION('',(0.,0.,-1.));
#346 = DIRECTION('',(-1.,0.,-0.));
#347 = ADVANCED_FACE('',(#348,#382),#393,.F.);
#348 = FACE_BOUND('',#349,.F.);
#349 = EDGE_LOOP('',(#350,#351,#358,#359,#366,#367,#374,#375));
#350 = ORIENTED_EDGE('',*,*,#266,.F.);
#351 = ORIENTED_EDGE('',*,*,#352,.T.);
#352 = EDGE_CURVE('',#258,#207,#353,.T.);
#353 = CIRCLE('',#354,4.);
#354 = AXIS2_PLACEMENT_3D('',#355,#356,#357);
#355 = CARTESIAN_POINT('',(4.,4.,0.));
#356 = DIRECTION('',(0.,0.,1.));
#357 = DIRECTION('',(0.,-1.,0.));
#358 = ORIENTED_EDGE('',*,*,#206,.T.);
#359 = ORIENTED_EDGE('',*,*,#360,.F.);
#360 = EDGE_CURVE('',#150,#209,#361,.T.);
#361 = CIRCLE('',#362,4.);
#362 = AXIS2_PLACEMENT_3D('',#363,#364,#365);
#363 = CARTESIAN_POINT('',(796.,4.,0.));
#364 = DIRECTION('',(-0.,-0.,-1.));
#365 = DIRECTION('',(0.,-1.,0.));
#366 = ORIENTED_EDGE('',*,*,#149,.T.);
#367 = ORIENTED_EDGE('',*,*,#368,.T.);
#368 = EDGE_CURVE('',#141,#75,#369,.T.);
#369 = CIRCLE('',#370,4.);
#370 = AXIS2_PLACEMENT_3D('',#371,#372,#373);
#371 = CARTESIAN_POINT('',(796.,36.,0.));
#372 = DIRECTION('',(0.,0.,1.));
#373 = DIRECTION('',(0.,-1.,0.));
#374 = ORIENTED_EDGE('',*,*,#72,.F.);
#375 = ORIENTED_EDGE('',*,*,#376,.F.);
#376 = EDGE_CURVE('',#267,#73,#377,.T.);
#377 = CIRCLE('',#378,4.);
#378 = AXIS2_PLACEMENT_3D('',#379,#380,#381);
#379 = CARTESIAN_POINT('',(4.,36.,0.));
#380 = DIRECTION('',(-0.,-0.,-1.));
#381 = DIRECTION('',(0.,-1.,0.));
#382 = FACE_BOUND('',#383,.F.);
#383 = EDGE_LOOP('',(#384));
#384 = ORIENTED_EDGE('',*,*,#385,.F.);
#385 = EDGE_CURVE('',#386,#386,#388,.T.);
#386 = VERTEX_POINT('',#387);
#387 = CARTESIAN_POINT('',(404.,20.,0.));
#388 = CIRCLE('',#389,4.);
#389 = AXIS2_PLACEMENT_3D('',#390,#391,#392);
#390 = CARTESIAN_POINT('',(400.,20.,0.));
#391 = DIRECTION('',(0.,0.,1.));
#392 = DIRECTION('',(1.,0.,-0.));
#393 = PLANE('',#394);
#394 = AXIS2_PLACEMENT_3D('',#395,#396,#397);
#395 = CARTESIAN_POINT('',(0.,0.,0.));
#396 = DIRECTION('',(0.,0.,1.));
#397 = DIRECTION('',(1.,0.,-0.));
#398 = ADVANCED_FACE('',(#399),#404,.T.);
#399 = FACE_BOUND('',#400,.T.);
#400 = EDGE_LOOP('',(#401,#402,#403));
#401 = ORIENTED_EDGE('',*,*,#140,.T.);
#402 = ORIENTED_EDGE('',*,*,#368,.T.);
#403 = ORIENTED_EDGE('',*,*,#105,.T.);
#404 = CYLINDRICAL_SURFACE('',#405,4.);
#405 = AXIS2_PLACEMENT_3D('',#406,#407,#408);
#406 = CARTESIAN_POINT('',(796.,36.,0.));
#407 = DIRECTION('',(0.,0.,1.));
#408 = DIRECTION('',(1.,0.,-0.));
#409 = ADVANCED_FACE('',(#410),#415,.T.);
#410 = FACE_BOUND('',#411,.F.);
#411 = EDGE_LOOP('',(#412,#413,#414));
#412 = ORIENTED_EDGE('',*,*,#274,.F.);
#413 = ORIENTED_EDGE('',*,*,#376,.T.);
#414 = ORIENTED_EDGE('',*,*,#82,.T.);
#415 = CYLINDRICAL_SURFACE('',#416,4.);
#416 = AXIS2_PLACEMENT_3D('',#417,#418,#419);
#417 = CARTESIAN_POINT('',(4.,36.,0.));
#418 = DIRECTION('',(0.,0.,1.));
#419 = DIRECTION('',(-1.,-0.,0.));
#420 = ADVANCED_FACE('',(#421),#440,.F.);
#421 = FACE_BOUND('',#422,.F.);
#422 = EDGE_LOOP('',(#423,#432,#438,#439));
#423 = ORIENTED_EDGE('',*,*,#424,.F.);
#424 = EDGE_CURVE('',#425,#425,#427,.T.);
#425 = VERTEX_POINT('',#426);
#426 = CARTESIAN_POINT('',(400.,38.,16.));
#427 = CIRCLE('',#428,4.);
#428 = AXIS2_PLACEMENT_3D('',#429,#430,#431);
#429 = CARTESIAN_POINT('',(400.,38.,20.));
#430 = DIRECTION('',(0.,-1.,0.));
#431 = DIRECTION('',(0.,-0.,-1.));
#432 = ORIENTED_EDGE('',*,*,#433,.F.);
#433 = EDGE_CURVE('',#114,#425,#434,.T.);
#434 = LINE('',#435,#436);
#435 = CARTESIAN_POINT('',(400.,41.,16.));
#436 = VECTOR('',#437,1.);
#437 = DIRECTION('',(0.,-1.,0.));
#438 = ORIENTED_EDGE('',*,*,#113,.T.);
#439 = ORIENTED_EDGE('',*,*,#433,.T.);
#440 = CYLINDRICAL_SURFACE('',#441,4.);
#441 = AXIS2_PLACEMENT_3D('',#442,#443,#444);
#442 = CARTESIAN_POINT('',(400.,41.,20.));
#443 = DIRECTION('',(0.,-1.,0.));
#444 = DIRECTION('',(0.,-0.,-1.));
#445 = ADVANCED_FACE('',(#446),#451,.T.);
#446 = FACE_BOUND('',#447,.F.);
#447 = EDGE_LOOP('',(#448,#449,#450));
#448 = ORIENTED_EDGE('',*,*,#157,.F.);
#449 = ORIENTED_EDGE('',*,*,#360,.T.);
#450 = ORIENTED_EDGE('',*,*,#232,.T.);
#451 = CYLINDRICAL_SURFACE('',#452,4.);
#452 = AXIS2_PLACEMENT_3D('',#453,#454,#455);
#453 = CARTESIAN_POINT('',(796.,4.,0.));
#454 = DIRECTION('',(0.,0.,1.));
#455 = DIRECTION('',(1.,0.,-0.));
#456 = ADVANCED_FACE('',(#457,#473),#484,.T.);
#457 = FACE_BOUND('',#458,.T.);
#458 = EDGE_LOOP('',(#459,#465,#466,#472));
#459 = ORIENTED_EDGE('',*,*,#460,.F.);
#460 = EDGE_CURVE('',#289,#167,#461,.T.);
#461 = LINE('',#462,#463);
#462 = CARTESIAN_POINT('',(0.,2.,2.));
#463 = VECTOR('',#464,1.);
#464 = DIRECTION('',(1.,0.,-0.));
#465 = ORIENTED_EDGE('',*,*,#286,.F.);
#466 = ORIENTED_EDGE('',*,*,#467,.T.);
#467 = EDGE_CURVE('',#287,#169,#468,.T.);
#468 = LINE('',#469,#470);
#469 = CARTESIAN_POINT('',(0.,2.,38.));
#470 = VECTOR('',#471,1.);
#471 = DIRECTION('',(1.,0.,-0.));
#472 = ORIENTED_EDGE('',*,*,#166,.F.);
#473 = FACE_BOUND('',#474,.T.);
#474 = EDGE_LOOP('',(#475));
#475 = ORIENTED_EDGE('',*,*,#476,.F.);
#476 = EDGE_CURVE('',#477,#477,#479,.T.);
#477 = VERTEX_POINT('',#478);
#478 = CARTESIAN_POINT('',(400.,2.,24.));
#479 = CIRCLE('',#480,4.);
#480 = AXIS2_PLACEMENT_3D('',#481,#482,#483);
#481 = CARTESIAN_POINT('',(400.,2.,20.));
#482 = DIRECTION('',(0.,1.,0.));
#483 = DIRECTION('',(0.,-0.,1.));
#484 = PLANE('',#485);
#485 = AXIS2_PLACEMENT_3D('',#486,#487,#488);
#486 = CARTESIAN_POINT('',(0.,2.,2.));
#487 = DIRECTION('',(-0.,1.,0.));
#488 = DIRECTION('',(0.,0.,1.));
#489 = ADVANCED_FACE('',(#490,#501),#504,.F.);
#490 = FACE_BOUND('',#491,.F.);
#491 = EDGE_LOOP('',(#492,#493,#494,#495));
#492 = ORIENTED_EDGE('',*,*,#296,.F.);
#493 = ORIENTED_EDGE('',*,*,#467,.T.);
#494 = ORIENTED_EDGE('',*,*,#192,.F.);
#495 = ORIENTED_EDGE('',*,*,#496,.F.);
#496 = EDGE_CURVE('',#297,#185,#497,.T.);
#497 = LINE('',#498,#499);
#498 = CARTESIAN_POINT('',(0.,38.,38.));
#499 = VECTOR('',#500,1.);
#500 = DIRECTION('',(1.,0.,-0.));
#501 = FACE_BOUND('',#502,.F.);
#502 = EDGE_LOOP('',(#503));
#503 = ORIENTED_EDGE('',*,*,#326,.T.);
#504 = PLANE('',#505);
#505 = AXIS2_PLACEMENT_3D('',#506,#507,#508);
#506 = CARTESIAN_POINT('',(0.,2.,38.));
#507 = DIRECTION('',(0.,0.,1.));
#508 = DIRECTION('',(1.,0.,-0.));
#509 = ADVANCED_FACE('',(#510,#521),#524,.F.);
#510 = FACE_BOUND('',#511,.F.);
#511 = EDGE_LOOP('',(#512,#518,#519,#520));
#512 = ORIENTED_EDGE('',*,*,#513,.F.);
#513 = EDGE_CURVE('',#305,#177,#514,.T.);
#514 = LINE('',#515,#516);
#515 = CARTESIAN_POINT('',(0.,38.,2.));
#516 = VECTOR('',#517,1.);
#517 = DIRECTION('',(1.,0.,-0.));
#518 = ORIENTED_EDGE('',*,*,#304,.F.);
#519 = ORIENTED_EDGE('',*,*,#496,.T.);
#520 = ORIENTED_EDGE('',*,*,#184,.F.);
#521 = FACE_BOUND('',#522,.F.);
#522 = EDGE_LOOP('',(#523));
#523 = ORIENTED_EDGE('',*,*,#424,.T.);
#524 = PLANE('',#525);
#525 = AXIS2_PLACEMENT_3D('',#526,#527,#528);
#526 = CARTESIAN_POINT('',(0.,38.,2.));
#527 = DIRECTION('',(-0.,1.,0.));
#528 = DIRECTION('',(0.,0.,1.));
#529 = ADVANCED_FACE('',(#530,#536),#547,.T.);
#530 = FACE_BOUND('',#531,.T.);
#531 = EDGE_LOOP('',(#532,#533,#534,#535));
#532 = ORIENTED_EDGE('',*,*,#312,.F.);
#533 = ORIENTED_EDGE('',*,*,#460,.T.);
#534 = ORIENTED_EDGE('',*,*,#176,.F.);
#535 = ORIENTED_EDGE('',*,*,#513,.F.);
#536 = FACE_BOUND('',#537,.T.);
#537 = EDGE_LOOP('',(#538));
#538 = ORIENTED_EDGE('',*,*,#539,.F.);
#539 = EDGE_CURVE('',#540,#540,#542,.T.);
#540 = VERTEX_POINT('',#541);
#541 = CARTESIAN_POINT('',(404.,20.,2.));
#542 = CIRCLE('',#543,4.);
#543 = AXIS2_PLACEMENT_3D('',#544,#545,#546);
#544 = CARTESIAN_POINT('',(400.,20.,2.));
#545 = DIRECTION('',(0.,0.,1.));
#546 = DIRECTION('',(1.,0.,-0.));
#547 = PLANE('',#548);
#548 = AXIS2_PLACEMENT_3D('',#549,#550,#551);
#549 = CARTESIAN_POINT('',(0.,2.,2.));
#550 = DIRECTION('',(0.,0.,1.));
#551 = DIRECTION('',(1.,0.,-0.));
#552 = ADVANCED_FACE('',(#553),#558,.T.);
#553 = FACE_BOUND('',#554,.T.);
#554 = EDGE_LOOP('',(#555,#556,#557));
#555 = ORIENTED_EDGE('',*,*,#257,.T.);
#556 = ORIENTED_EDGE('',*,*,#352,.T.);
#557 = ORIENTED_EDGE('',*,*,#216,.T.);
#558 = CYLINDRICAL_SURFACE('',#559,4.);
#559 = AXIS2_PLACEMENT_3D('',#560,#561,#562);
#560 = CARTESIAN_POINT('',(4.,4.,0.));
#561 = DIRECTION('',(0.,0.,1.));
#562 = DIRECTION('',(-1.,-0.,0.));
#563 = ADVANCED_FACE('',(#564),#575,.F.);
#564 = FACE_BOUND('',#565,.F.);
#565 = EDGE_LOOP('',(#566,#567,#573,#574));
#566 = ORIENTED_EDGE('',*,*,#476,.F.);
#567 = ORIENTED_EDGE('',*,*,#568,.F.);
#568 = EDGE_CURVE('',#241,#477,#569,.T.);
#569 = LINE('',#570,#571);
#570 = CARTESIAN_POINT('',(400.,-1.,24.));
#571 = VECTOR('',#572,1.);
#572 = DIRECTION('',(0.,1.,0.));
#573 = ORIENTED_EDGE('',*,*,#240,.T.);
#574 = ORIENTED_EDGE('',*,*,#568,.T.);
#575 = CYLINDRICAL_SURFACE('',#576,4.);
#576 = AXIS2_PLACEMENT_3D('',#577,#578,#579);
#577 = CARTESIAN_POINT('',(400.,-1.,20.));
#578 = DIRECTION('',(0.,1.,0.));
#579 = DIRECTION('',(0.,-0.,1.));
#580 = ADVANCED_FACE('',(#581),#592,.F.);
#581 = FACE_BOUND('',#582,.F.);
#582 = EDGE_LOOP('',(#583,#584,#590,#591));
#583 = ORIENTED_EDGE('',*,*,#539,.F.);
#584 = ORIENTED_EDGE('',*,*,#585,.F.);
#585 = EDGE_CURVE('',#386,#540,#586,.T.);
#586 = LINE('',#587,#588);
#587 = CARTESIAN_POINT('',(404.,20.,-1.));
#588 = VECTOR('',#589,1.);
#589 = DIRECTION('',(0.,0.,1.));
#590 = ORIENTED_EDGE('',*,*,#385,.T.);
#591 = ORIENTED_EDGE('',*,*,#585,.T.);
#592 = CYLINDRICAL_SURFACE('',#593,4.);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(400.,20.,-1.));
#595 = DIRECTION('',(0.,0.,1.));
#596 = DIRECTION('',(1.,0.,-0.));
#597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#601)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#598,#599,#600)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#598 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#599 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#600 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#601 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.E-07),#598,
  'distance_accuracy_value','confusion accuracy');
#602 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
