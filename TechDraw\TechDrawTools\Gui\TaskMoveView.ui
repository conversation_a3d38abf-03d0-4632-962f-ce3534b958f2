<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TaskMoveView</class>
 <widget class="QWidget" name="TaskMoveView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>159</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Move View</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout" columnstretch="2,3,0">
     <item row="0" column="0">
      <widget class="QLabel" name="lViewName">
       <property name="text">
        <string>View to move</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="leView">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <widget class="QPushButton" name="pbView">
       <property name="text">
        <string notr="true">...</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="lFromPage">
       <property name="text">
        <string>From Page</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLineEdit" name="leFromPage">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QPushButton" name="pbFromPage">
       <property name="text">
        <string notr="true">...</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="lToPage">
       <property name="text">
        <string>To Page</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QLineEdit" name="leToPage">
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QPushButton" name="pbToPage">
       <property name="text">
        <string notr="true">...</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
